#!/usr/bin/env python3
"""
Test script to demonstrate multi-CLS functionality in the Retrieval_MLM pipeline.
This script shows how to switch between CLS+EOS and multi-CLS approaches.
"""

import torch
from models import load_all_configs_from_yaml, MaskedLanguageModel
from transformers import AutoTokenizer

def test_multi_cls_configuration():
    """Test both CLS+EOS and multi-CLS configurations"""
    
    print("🧪 Testing Multi-CLS Configuration")
    print("=" * 50)
    
    # Load configurations
    logging_config, data_config, training_config, model_config, wandb_config = load_all_configs_from_yaml("config.yaml")
    
    # Initialize tokenizer - use GPT-2 as fallback if configured tokenizer fails
    try:
        tokenizer = AutoTokenizer.from_pretrained(data_config.tokenizer_path)
        print(f"📝 Using configured tokenizer: {data_config.tokenizer_path}")
    except Exception as e:
        print(f"⚠️  Failed to load configured tokenizer ({e}), using GPT-2 fallback")
        tokenizer = AutoTokenizer.from_pretrained("gpt2")
        
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    if tokenizer.cls_token is None:
        tokenizer.add_special_tokens({'cls_token': '[CLS]'})
    if tokenizer.mask_token is None:
        tokenizer.add_special_tokens({'mask_token': '[MASK]'})
    
    print(f"📝 Tokenizer vocab size: {len(tokenizer)}")
    print(f"📝 Special tokens - PAD: {tokenizer.pad_token_id}, CLS: {tokenizer.cls_token_id}, EOS: {tokenizer.eos_token_id}, MASK: {tokenizer.mask_token_id}")
    print(f"🔧 Model config - use_multi_cls: {model_config.use_multi_cls}")
    print(f"🔧 Model config - n_cls_tokens: {model_config.n_cls_tokens}")
    print(f"🔧 Model config - cls_embedding_lr_scale: {model_config.cls_embedding_lr_scale}")
    print()
    
    # Test model initialization
    try:
        model = MaskedLanguageModel(model_config, tokenizer=tokenizer)
        print(f"✅ Model initialized successfully!")
        print(f"   - Strategy: {'Multi-CLS' if model.use_multi_cls else 'CLS+EOS'}")
        print(f"   - CLS tokens: {model.n_cls_tokens}")
        
        # Debug: Check CLS token IDs
        if model.use_multi_cls:
            if isinstance(model.cls_token_ids, list) and len(model.cls_token_ids) >= 2:
                print(f"   - CLS token IDs: {model.cls_token_ids[:5]}...{model.cls_token_ids[-2:]}")  # Show first 5 and last 2
            else:
                print(f"   - CLS token IDs: {model.cls_token_ids} (type: {type(model.cls_token_ids)})")
        
        # Count parameters
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"   - Total parameters: {total_params:,}")
        print(f"   - Trainable parameters: {trainable_params:,}")
        
        if model.use_multi_cls:
            cls_params = model.cls_embeddings.numel()
            print(f"   - CLS embedding parameters: {cls_params:,}")
        
        print()
        
        # Test forward pass with dummy data
        B, T = 2, 64
        
        # Create dummy input that respects the multi-CLS structure
        if model.use_multi_cls:
            # For multi-CLS: [CLS1, CLS2, ..., CLS16, content..., EOS, PAD...]
            dummy_input = torch.zeros(B, T, dtype=torch.long)
            for b in range(B):
                # Add CLS tokens (with safety check)
                if isinstance(model.cls_token_ids, list):
                    for i in range(min(model.n_cls_tokens, T, len(model.cls_token_ids))):
                        dummy_input[b, i] = model.cls_token_ids[i]
                else:
                    print(f"⚠️  Warning: cls_token_ids is not a list: {model.cls_token_ids}")
                    # Fallback: use model.cls_token_id for all positions
                    for i in range(min(model.n_cls_tokens, T)):
                        dummy_input[b, i] = model.cls_token_id
                
                # Add some content tokens
                content_start = model.n_cls_tokens
                content_end = min(T - 1, content_start + 20)  # Leave space for EOS
                if content_start < content_end:
                    dummy_input[b, content_start:content_end] = torch.randint(0, min(1000, len(tokenizer)), (content_end - content_start,))
                # Add EOS token
                if content_end < T:
                    dummy_input[b, content_end] = model.eos_token_id
                # Rest will be PAD (0)
        else:
            # Standard approach: random tokens
            dummy_input = torch.randint(0, len(tokenizer), (B, T))
        
        dummy_labels = torch.full_like(dummy_input, -100)
        dummy_attention_mask = torch.ones_like(dummy_input)
        
        # Mask a few tokens for testing (avoid CLS tokens)
        mask_start = model.n_cls_tokens if model.use_multi_cls else 1
        mask_end = min(mask_start + 5, T - 1)  # Avoid EOS
        if mask_start < mask_end:
            dummy_labels[:, mask_start:mask_end] = dummy_input[:, mask_start:mask_end]
        
        print(f"🔄 Testing forward pass with dummy data (B={B}, T={T})")
        
        try:
            with torch.no_grad():
                logits, loss = model(dummy_input, attention_mask=dummy_attention_mask, labels=dummy_labels)
            
            print(f"   - Logits shape: {logits.shape}")
            print(f"   - Loss: {loss.item():.4f}")
            print(f"✅ Forward pass successful!")
            print()
        except Exception as e:
            print(f"❌ Forward pass failed: {e}")
            print(f"   - Error type: {type(e).__name__}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test attention mechanism specifically
        print("🔍 Testing attention mechanism with multi-CLS")
        if model.use_multi_cls:
            print(f"   - Multi-CLS attention enabled with {model.n_cls_tokens} CLS tokens")
            print(f"   - Custom RoPE positioning: {'Enabled' if model.transformer.h[0].attn.use_multi_cls else 'Disabled'}")
        else:
            print("   - Standard CLS+EOS attention")
        print()
        
        # Test optimizer configuration
        print("🔧 Testing optimizer configuration")
        optimizer = model.configure_optimizers(
            weight_decay=0.1, 
            learning_rate=2e-4, 
            device_type="cpu"
        )
        print(f"   - Optimizer groups: {len(optimizer.param_groups)}")
        for i, group in enumerate(optimizer.param_groups):
            print(f"   - Group {i}: lr={group['lr']:.2e}, weight_decay={group['weight_decay']}, params={len(group['params'])}")
        print()
        
    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        return False
    
    return True

def show_configuration_examples():
    """Show examples of how to configure multi-CLS"""
    
    print("📋 Configuration Examples")
    print("=" * 50)
    
    print("🔹 To enable Multi-CLS (16 tokens):")
    print("   model:")
    print("     use_multi_cls: true")
    print("     n_cls_tokens: 16")
    print("     cls_embedding_lr_scale: 0.1")
    print()
    
    print("🔹 To use standard CLS+EOS:")
    print("   model:")
    print("     use_multi_cls: false")
    print()
    
    print("🔹 Training commands:")
    print("   # With multi-CLS")
    print("   python train.py  # (after setting use_multi_cls: true)")
    print()
    print("   # With standard CLS+EOS")
    print("   python train.py  # (after setting use_multi_cls: false)")
    print()

if __name__ == "__main__":
    print("🚀 Multi-CLS Integration Test")
    print("=" * 50)
    print()
    
    # Test the configuration
    success = test_multi_cls_configuration()
    
    if success:
        print("🎉 All tests passed!")
        print()
        show_configuration_examples()
    else:
        print("❌ Tests failed. Please check your configuration.")
    
    print("=" * 50)
    print("✨ Multi-CLS integration is ready to use!")
