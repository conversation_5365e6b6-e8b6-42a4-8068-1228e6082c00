#!/usr/bin/env python3
"""
Simple test for multi-CLS functionality without external dependencies.
"""

import torch
from models.config import ModelConfig
from models.masked_language_model import MaskedLanguageModel
from transformers import AutoTokenizer

def simple_test():
    print("🧪 Simple Multi-CLS Test")
    print("=" * 40)
    
    # Create a simple model config
    model_config = ModelConfig(
        block_size=64,
        vocab_size=1000,
        n_layer=2,
        n_head=8,  # Must divide evenly into n_embd
        n_embd=128,
        n_head_4=8,
        mask_prob=0.15,
        use_multi_cls=True,
        n_cls_tokens=16,  # Use fewer CLS tokens for testing
        cls_embedding_lr_scale=0.1
    )
    
    print(f"✅ Model config created")
    print(f"   - use_multi_cls: {model_config.use_multi_cls}")
    print(f"   - n_cls_tokens: {model_config.n_cls_tokens}")
    
    try:
        # Test without tokenizer first (fallback mode)
        print("\n🔄 Testing model initialization (fallback mode)")
        model = MaskedLanguageModel(model_config, tokenizer=None)
        print(f"✅ Model initialized successfully!")
        print(f"   - Strategy: {'Multi-CLS' if model.use_multi_cls else 'CLS+EOS'}")
        print(f"   - CLS tokens: {model.n_cls_tokens}")
        print(f"   - CLS token IDs: {model.cls_token_ids}")
        
        # Test forward pass
        B, T = 2, 32
        print(f"\n🔄 Testing forward pass (B={B}, T={T})")
        
        # Create simple dummy input
        if model.use_multi_cls:
            dummy_input = torch.zeros(B, T, dtype=torch.long)
            # Set CLS tokens
            for i in range(min(model.n_cls_tokens, T)):
                dummy_input[:, i] = model.cls_token_ids[i]
            # Set some content
            dummy_input[:, model.n_cls_tokens:T-1] = torch.randint(0, 100, (B, T-1-model.n_cls_tokens))
            # Set EOS
            dummy_input[:, T-1] = model.eos_token_id
        else:
            dummy_input = torch.randint(0, 1000, (B, T))
        
        dummy_labels = torch.full_like(dummy_input, -100)
        dummy_attention_mask = torch.ones_like(dummy_input)
        
        # Mask some tokens for testing
        mask_start = model.n_cls_tokens if model.use_multi_cls else 1
        mask_end = min(mask_start + 3, T - 1)
        if mask_start < mask_end:
            dummy_labels[:, mask_start:mask_end] = dummy_input[:, mask_start:mask_end]
        
        with torch.no_grad():
            logits, loss = model(dummy_input, attention_mask=dummy_attention_mask, labels=dummy_labels)
        
        print(f"   - Input shape: {dummy_input.shape}")
        print(f"   - Logits shape: {logits.shape}")
        print(f"   - Loss: {loss.item():.4f}")
        print(f"✅ Forward pass successful!")
        
        # Test parameter counts
        total_params = sum(p.numel() for p in model.parameters())
        if model.use_multi_cls:
            cls_params = model.cls_embeddings.numel()
            print(f"   - Total parameters: {total_params:,}")
            print(f"   - CLS embedding parameters: {cls_params:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = simple_test()
    if success:
        print("\n🎉 Simple test passed!")
    else:
        print("\n❌ Simple test failed!")
