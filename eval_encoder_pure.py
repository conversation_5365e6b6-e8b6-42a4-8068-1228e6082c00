import os
import sys
import math
import argparse
import json
import yaml
from typing import List, Dict, <PERSON><PERSON>
from datetime import datetime

import torch
import torch.nn.functional as F

# External deps expected:
#   pip install beir transformers sentencepiece
try:
    from beir import util
    from beir.datasets.data_loader import GenericDataLoader
except Exception as e:
    raise RuntimeError(f"BEIR base import failed: {e}. Try: pip install beir") from e

_eval_import_error = None
try:
    from beir.retrieval.evaluation import EvaluateR<PERSON>rie<PERSON> as Evaluate
except Exception as e:
    Evaluate = None
    _eval_import_error = e

try:
    from transformers import AutoTokenizer
except Exception:
    AutoTokenizer = None

sys.path.append(os.path.dirname(__file__))
from models import MaskedLanguageModel


def build_tokenizer(tokenizer_path: str):
    """Build tokenizer using AutoTokenizer from specified path."""
    if AutoTokenizer is None:
        raise RuntimeError("transformers not installed. Please: pip install transformers")
    
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
    
    # Add special tokens if missing
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    if tokenizer.mask_token is None:
        tokenizer.add_special_tokens({'mask_token': '[MASK]'})
    if tokenizer.cls_token is None:
        tokenizer.add_special_tokens({'cls_token': '[CLS]'})
    
    # Get special token IDs
    mask_id = tokenizer.mask_token_id
    pad_id = tokenizer.pad_token_id
    cls_id = tokenizer.cls_token_id
    
    print(f"[Info] Tokenizer vocab size: {tokenizer.vocab_size}")
    print(f"[Info] Special tokens - MASK: {mask_id}, PAD: {pad_id}, CLS: {cls_id}")
    
    return tokenizer, mask_id, pad_id, cls_id, tokenizer.vocab_size


def batch_encode_with_cls(tokenizer, texts: List[str], cls_id: int, pad_id: int, max_len: int, device: torch.device, effective_vocab_size: int, prepend_cls: bool = False):
    """Encode texts with proper tokenization and padding."""
    enc = tokenizer(texts, add_special_tokens=True, padding=False, truncation=True, 
                   max_length=max_len, return_attention_mask=True)
    input_ids = []
    for ids in enc["input_ids"]:
        ids = ids[:max_len]
        ids = [min(token_id, effective_vocab_size-1) for token_id in ids]
        if prepend_cls:
            ids = ([cls_id] + ids)[:max_len]
        if len(ids) < max_len:
            ids = ids + [pad_id] * (max_len - len(ids))
        input_ids.append(ids)
    input_ids = torch.tensor(input_ids, dtype=torch.long, device=device)
    attention_mask = (input_ids != pad_id).long()
    return input_ids, attention_mask


def get_cls_embeddings(model: MaskedLanguageModel, input_ids: torch.Tensor, attention_mask: torch.Tensor, normalize: bool = True, pooling: str = "cls"):
    # Run only the transformer + ln_f and pool
    x = model.transformer.wte(input_ids)
    for block in model.transformer.h:
        x = block(x, attention_mask=attention_mask)
    x = model.transformer.ln_f(x)
    if pooling == "mean":
        # mean over non-pad tokens
        mask = attention_mask.to(x.dtype)  # (B, T)
        denom = mask.sum(dim=1, keepdim=True).clamp_min(1.0)  # (B,1)
        pooled = (x * mask.unsqueeze(-1)).sum(dim=1) / denom
    elif pooling == "cls_eos_avg":
        # Average of CLS (first token) and EOS (last non-pad token) embeddings
        cls_emb = x[:, 0, :]  # CLS token at position 0
        
        # Find EOS token positions (last non-pad token for each sequence)
        seq_lengths = attention_mask.sum(dim=1)  # Length of each sequence
        batch_size = x.size(0)
        eos_emb = torch.zeros_like(cls_emb)
        
        for i in range(batch_size):
            # Get the last non-pad token position
            last_pos = seq_lengths[i] - 1
            eos_emb[i] = x[i, last_pos, :]
        
        # Average CLS and EOS embeddings
        pooled = (cls_emb + eos_emb) / 2.0
    else:
        pooled = x[:, 0, :]
    if normalize:
        pooled = F.normalize(pooled, p=2, dim=-1)
    return pooled


def compute_results(queries: Dict[str, str], corpus: Dict[str, Dict[str, str]], qrels: Dict[str, Dict[str, int]],
                    model: MaskedLanguageModel, tokenizer, cls_id: int, pad_id: int, batch_size: int, max_len: int, device: torch.device, effective_vocab_size: int, pooling: str = "cls", prepend_cls: bool = False) -> Dict[str, Dict[str, float]]:
    # Encode corpus to embeddings
    doc_ids = list(corpus.keys())
    doc_texts = [corpus[did]["text"] if corpus[did].get("title") is None else (corpus[did]["title"] + " \n" + corpus[did]["text"]) for did in doc_ids]
    all_doc_embs = []
    model.eval()
    with torch.no_grad():
        for i in range(0, len(doc_texts), batch_size):
            batch_texts = doc_texts[i:i+batch_size]
            input_ids, attn = batch_encode_with_cls(tokenizer, batch_texts, cls_id, pad_id, max_len, device, effective_vocab_size, prepend_cls=prepend_cls)
            embs = get_cls_embeddings(model, input_ids, attn, normalize=True, pooling=pooling)
            all_doc_embs.append(embs)
    doc_matrix = torch.cat(all_doc_embs, dim=0)  # [N_docs, dim]

    # Map doc id to index
    doc_index = {did: idx for idx, did in enumerate(doc_ids)}

    # Encode queries and compute scores
    results: Dict[str, Dict[str, float]] = {}
    with torch.no_grad():
        for i, (qid, qtext) in enumerate(queries.items()):
            q_input, q_attn = batch_encode_with_cls(tokenizer, [qtext], cls_id, pad_id, max_len, device, effective_vocab_size, prepend_cls=prepend_cls)
            q_emb = get_cls_embeddings(model, q_input, q_attn, normalize=True, pooling=pooling)  # [1, dim]
            # cosine similarity via dot product of normalized vectors
            scores = torch.matmul(q_emb, doc_matrix.T).squeeze(0)  # [N_docs]
            # Convert to dict of top scores (all docs). For efficiency, take top 1000.
            topk = min(1000, scores.size(0))
            vals, idxs = torch.topk(scores, k=topk, largest=True)
            res = {doc_ids[j.item()]: float(vals[k].item()) for k, j in enumerate(idxs)}
            results[qid] = res
    return results


def load_config(config_path: str) -> Dict:
    """Load configuration from YAML file"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def save_results_as_markdown_table(manual_metrics: Dict, metadata: Dict[str, str], k_values: List[int]) -> str:
    """Create a markdown table with evaluation results and metadata."""
    
    # Create the main results table
    markdown = "# Evaluation Results\n\n"
    
    # Add metadata section with bold formatting
    markdown += f"**Evaluation Time:** {metadata.get('Date', 'N/A')}\n\n"
    markdown += f"**Dataset:** {metadata.get('Dataset', 'N/A')}\n\n"
    markdown += f"**Checkpoint:** {metadata.get('checkpoint', 'N/A')}\n\n"
    markdown += f"**Tokenizer:** {metadata.get('Tokenizer', 'N/A')}\n\n"
    markdown += f"**Max_len:** {metadata.get('Max Length', 'N/A')}\n\n"
    markdown += f"**Pooling Strategy:** {metadata.get('Pooling', 'N/A')}\n\n"
    
    # Format Prepend CLS as Yes/No
    prepend_cls = metadata.get('Prepend CLS', 'False')
    prepend_cls_text = "Yes" if prepend_cls == 'True' else "No"
    markdown += f"**Prepend CLS Token:** {prepend_cls_text}\n\n"
    
    # Add results table
    markdown += "## Summary Metrics\n\n"
    markdown += "| k | NDCG | Recall | Precision |\n"
    markdown += "|---|------|--------|-----------|\n"
    
    for k in k_values:
        ndcg = manual_metrics[k]['ndcg']
        recall = manual_metrics[k]['recall']
        precision = manual_metrics[k]['precision']
        markdown += f"| {k} | {ndcg:.4f} | {recall:.4f} | {precision:.4f} |\n"
    
    # Add primary metric
    ndcg_10 = manual_metrics.get(10, {}).get('ndcg', 0.0)
    markdown += f"\n**Primary Metric (NDCG@10):** {ndcg_10:.4f}\n"
    
    return markdown

def save_results_to_file(manual_metrics: Dict, metadata: Dict[str, str], k_values: List[int], eval_dir: str = "eval"):
    """Save evaluation results to a timestamped markdown file in the eval directory."""
    
    # Create eval directory if it doesn't exist
    os.makedirs(eval_dir, exist_ok=True)
    
    # Generate filename with timestamp and dataset name
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    dataset_name = metadata.get("Dataset", "unknown")
    filename = f"eval_results_{dataset_name}_{timestamp}.md"
    filepath = os.path.join(eval_dir, filename)
    
    # Generate markdown content
    markdown_content = save_results_as_markdown_table(manual_metrics, metadata, k_values)
    
    # Write to file
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    
    print(f"\nResults saved to: {filepath}")
    return filepath


def main():
    parser = argparse.ArgumentParser(description="Evaluate CLS embeddings with NDCG@10 on a BEIR dataset")
    parser.add_argument("--config", type=str, default=None, help="Path to YAML config file")
    parser.add_argument("--checkpoint", type=str, default=None, help="Path to model checkpoint (.pt) with state_dict under key 'model' or raw state_dict")
    parser.add_argument("--dataset", type=str, default="scifact", help="BEIR dataset name, e.g., scifact, trec-covid, nfcorpus, fiqa, dbpedia-entity, arguana, scidocs")
    parser.add_argument("--tokenizer_path", type=str, default="/home/<USER>/.cache/huggingface/hub/models--BAAI--bge-m3/snapshots/5617a9f61b028005a4858fdac845db406aefb181", help="Path to tokenizer")
    parser.add_argument("--data_dir", type=str, default="./beir_datasets", help="Directory to download/extract BEIR datasets")
    parser.add_argument("--batch_size", type=int, default=64)
    parser.add_argument("--max_len", type=int, default=256)
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu")
    parser.add_argument("--pooling", type=str, choices=["cls", "mean", "cls_eos_avg"], default="cls", help="Pooling strategy for fixed-size embeddings")
    parser.add_argument("--prepend_cls", action="store_true", help="Prepend a dedicated [CLS] token at position 0. Off by default; model likely uses first token as CLS during training.")
    args = parser.parse_args()
    
    # Initialize eval_dir with default value
    eval_dir = "eval"
    
    # Load config if provided and override args
    if args.config:
        config = load_config(args.config)
        eval_config = config.get('evaluation', {})
        model_config = config.get('model', {})
        data_config = config.get('data', {})
        
        # Override args with config values
        args.checkpoint = eval_config.get('checkpoint', args.checkpoint)
        args.dataset = eval_config.get('dataset', args.dataset)
        args.tokenizer_path = data_config.get('tokenizer_path', args.tokenizer_path)
        args.data_dir = eval_config.get('data_dir', args.data_dir)
        args.batch_size = eval_config.get('batch_size', args.batch_size)
        args.max_len = eval_config.get('max_len', args.max_len)
        args.device = eval_config.get('device', args.device)
        args.pooling = eval_config.get('pooling', args.pooling)
        args.prepend_cls = eval_config.get('prepend_cls', args.prepend_cls)
        
        # Get eval_dir from config
        eval_dir = eval_config.get('eval_dir', eval_dir)

    if Evaluate is None:
        print(f"BEIR evaluation import failed: {_eval_import_error}. Try: pip install pytrec-eval-terrier or pip install 'beir[eval]'")
        sys.exit(1)

    # Download & load dataset
    os.makedirs(args.data_dir, exist_ok=True)
    dataset_url = util.download_and_unzip("https://public.ukp.informatik.tu-darmstadt.de/thakur/BEIR/datasets/" + args.dataset + ".zip", args.data_dir)
    data_folder = os.path.join(args.data_dir, args.dataset)
    corpus, queries, qrels = GenericDataLoader(data_folder).load(split="test")

    # Build model
    device = torch.device(args.device)
    
    # Initialize tokenizer first
    tokenizer, mask_id, pad_id, cls_id, vocab_size = build_tokenizer(args.tokenizer_path)
    
    # Create model config
    if args.config:
        from models.config import load_model_config_from_yaml
        model_config = load_model_config_from_yaml(args.config)
    else:
        from models.config import ModelConfig
        model_config = ModelConfig()
    
    model = MaskedLanguageModel(model_config, tokenizer).to(device)

    # Resize embeddings for special tokens already handled in __init__ (effective_vocab = vocab+3)

    # Load checkpoint if provided
    if args.checkpoint is not None:
        # Use weights_only=False to handle custom classes like EncoderConfig
        ckpt = torch.load(args.checkpoint, map_location=device, weights_only=False)
        state_dict = ckpt.get("model", ckpt)
        
        # Handle compiled models (torch.compile adds _orig_mod. prefix)
        if any(key.startswith("_orig_mod.") for key in state_dict.keys()):
            print("[Info] Detected compiled model checkpoint, removing _orig_mod. prefix")
            state_dict = {key.replace("_orig_mod.", ""): value for key, value in state_dict.items()}
        
        missing, unexpected = model.load_state_dict(state_dict, strict=False)
        if missing:
            print(f"[Warn] Missing keys: {len(missing)} e.g., {missing[:5]}")
        if unexpected:
            print(f"[Warn] Unexpected keys: {len(unexpected)} e.g., {unexpected[:5]}")

    model.eval()

    # Use the tokenizer and special token IDs from model initialization
    effective_vocab_size = vocab_size

    # Compute results
    results = compute_results(queries, corpus, qrels, model, tokenizer, cls_id, pad_id, args.batch_size, args.max_len, device, effective_vocab_size, pooling=args.pooling, prepend_cls=args.prepend_cls)

    # Add diagnostics to understand why metrics are zero
    print("\n[Diagnostics]")
    total_queries = len(queries)
    queries_with_hits_10 = 0
    queries_with_hits_100 = 0
    
    for qid, qtext in list(queries.items())[:3]:  # Check first 3 queries as examples
        if qid in results and qid in qrels:
            top_docs = list(results[qid].keys())[:10]
            relevant_docs = set(qrels[qid].keys())
            hits_in_top10 = [doc for doc in top_docs if doc in relevant_docs]
            print(f"Query '{qid}': '{qtext[:60]}...'")
            print(f"  Top-5 retrieved: {top_docs[:5]}")
            print(f"  Relevant docs: {list(relevant_docs)[:5]}")
            print(f"  Hits in top-10: {hits_in_top10}")
            print()
    
    # Count overall hit rates
    for qid in queries:
        if qid in results and qid in qrels:
            top_10 = set(list(results[qid].keys())[:10])
            top_100 = set(list(results[qid].keys())[:100])
            relevant = set(qrels[qid].keys())
            
            if top_10 & relevant:
                queries_with_hits_10 += 1
            if top_100 & relevant:
                queries_with_hits_100 += 1
    
    print(f"Queries with hits@10: {queries_with_hits_10}/{total_queries} ({100*queries_with_hits_10/total_queries:.1f}%)")
    print(f"Queries with hits@100: {queries_with_hits_100}/{total_queries} ({100*queries_with_hits_100/total_queries:.1f}%)")
    print()

    # Manual evaluation to bypass BEIR issues
    def compute_manual_metrics(qrels, results, k_values):
        metrics = {k: {'ndcg': [], 'recall': [], 'precision': []} for k in k_values}
        
        for qid in qrels:
            if qid not in results:
                continue
                
            # Get top-k results and relevant docs
            ranked_docs = list(results[qid].keys())
            relevant_docs = set(qrels[qid].keys())
            
            for k in k_values:
                top_k = ranked_docs[:k]
                relevant_in_k = [doc for doc in top_k if doc in relevant_docs]
                
                # Recall@k
                recall_k = len(relevant_in_k) / len(relevant_docs) if relevant_docs else 0.0
                metrics[k]['recall'].append(recall_k)
                
                # Precision@k  
                precision_k = len(relevant_in_k) / k if k > 0 else 0.0
                metrics[k]['precision'].append(precision_k)
                
                # Simple NDCG@k (binary relevance)
                dcg = sum(1.0 / math.log2(i + 2) for i, doc in enumerate(top_k) if doc in relevant_docs)
                idcg = sum(1.0 / math.log2(i + 2) for i in range(min(k, len(relevant_docs))))
                ndcg_k = dcg / idcg if idcg > 0 else 0.0
                metrics[k]['ndcg'].append(ndcg_k)
        
        # Average across queries
        avg_metrics = {}
        for k in k_values:
            avg_metrics[k] = {
                'ndcg': sum(metrics[k]['ndcg']) / len(metrics[k]['ndcg']) if metrics[k]['ndcg'] else 0.0,
                'recall': sum(metrics[k]['recall']) / len(metrics[k]['recall']) if metrics[k]['recall'] else 0.0,
                'precision': sum(metrics[k]['precision']) / len(metrics[k]['precision']) if metrics[k]['precision'] else 0.0
            }
        return avg_metrics

    # Compute manual metrics
    k_values = [1, 3, 5, 10, 100]
    manual_metrics = compute_manual_metrics(qrels, results, k_values)
    
    print("Manual BEIR-style metrics:")
    print(f"NDCG@10: {manual_metrics[10]['ndcg']:.4f}")
    print("Summary metrics:")
    for k in k_values:
        nd = manual_metrics[k]['ndcg']
        rc = manual_metrics[k]['recall'] 
        pr = manual_metrics[k]['precision']
        print(f"k={k}: NDCG={nd:.4f}, Recall={rc:.4f}, P@k={pr:.4f}")
    
    # Try BEIR evaluator as backup
    try:
        evaluator = Evaluate()
        ndcg, _map, recall, precision = evaluator.evaluate(qrels, results, k_values=[10])
        ndcg10 = ndcg.get(10)
        print(f"\nBEIR NDCG@10: {0.0 if ndcg10 is None else ndcg10:.4f}")
    except Exception as e:
        print(f"\nBEIR evaluation failed: {e}")

    metadata = {
        "Date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "Dataset": args.dataset,
        "checkpoint": os.path.basename(args.checkpoint) if args.checkpoint else "None",
        "Model": "MaskedLanguageModel",
        "Tokenizer": args.tokenizer_path,
        "Pooling": args.pooling,
        "Prepend CLS": args.prepend_cls,
        "Batch Size": args.batch_size,
        "Max Length": args.max_len,
        "Device": args.device
    }
    save_results_to_file(manual_metrics, metadata, k_values, eval_dir)


if __name__ == "__main__":
    main()
