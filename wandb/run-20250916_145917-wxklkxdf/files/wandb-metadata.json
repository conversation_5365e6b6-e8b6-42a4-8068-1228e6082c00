{"os": "Linux-5.15.0-153-generic-x86_64-with-glibc2.35", "python": "CPython 3.12.9", "startedAt": "2025-09-16T11:29:17.934959Z", "program": "/home/<USER>/notebooks/<PERSON>_embedding/<PERSON>_embedding/train.py", "codePath": "train.py", "codePathLocal": "train.py", "email": "<EMAIL>", "root": "/home/<USER>/notebooks/<PERSON>_embedding/<PERSON>_embedding", "host": "h82", "executable": "/opt/anaconda3/envs/llm_env/bin/python3.12", "cpu_count": 112, "cpu_count_logical": 112, "gpu": "NVIDIA H100 80GB HBM3", "gpu_count": 8, "disk": {"/": {"total": "1511024451584", "used": "1438448746496"}}, "memory": {"total": "1651351687168"}, "gpu_nvidia": [{"name": "NVIDIA H100 80GB HBM3", "memoryTotal": "85520809984", "cudaCores": 16896, "architecture": "<PERSON>", "uuid": "GPU-77194a63-0847-1f11-ca76-93b042e00e4e"}, {"name": "NVIDIA H100 80GB HBM3", "memoryTotal": "85520809984", "cudaCores": 16896, "architecture": "<PERSON>", "uuid": "GPU-a4339cc5-9120-434c-0efd-72e07743bd78"}, {"name": "NVIDIA H100 80GB HBM3", "memoryTotal": "85520809984", "cudaCores": 16896, "architecture": "<PERSON>", "uuid": "GPU-23269b3b-5279-4644-f19a-9271181482ea"}, {"name": "NVIDIA H100 80GB HBM3", "memoryTotal": "85520809984", "cudaCores": 16896, "architecture": "<PERSON>", "uuid": "GPU-ba724632-f670-4b14-dafd-acce0f0e9962"}, {"name": "NVIDIA H100 80GB HBM3", "memoryTotal": "85520809984", "cudaCores": 16896, "architecture": "<PERSON>", "uuid": "GPU-488886e3-ab3f-2a65-b7cf-3e53717dd5d0"}, {"name": "NVIDIA H100 80GB HBM3", "memoryTotal": "85520809984", "cudaCores": 16896, "architecture": "<PERSON>", "uuid": "GPU-83acd5d5-35cc-9096-017a-4ca54c2b2e4e"}, {"name": "NVIDIA H100 80GB HBM3", "memoryTotal": "85520809984", "cudaCores": 16896, "architecture": "<PERSON>", "uuid": "GPU-2db63487-870f-eb77-f005-971e9a97ef57"}, {"name": "NVIDIA H100 80GB HBM3", "memoryTotal": "85520809984", "cudaCores": 16896, "architecture": "<PERSON>", "uuid": "GPU-347d855e-f066-042d-3ee4-3a8d5584a599"}], "cudaVersion": "12.8", "writerId": "fmytes3za85gxdungowt6mszwrbctr5q"}