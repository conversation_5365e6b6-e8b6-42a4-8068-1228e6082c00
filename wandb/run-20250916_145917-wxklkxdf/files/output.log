Wandb initialized: https://wandb.ai/axiomlaborg-axiom-lab/encoder-mlm-ali/runs/wxklkxdf
Tokenizer vocab size: 50368
total desired batch size: 1572864
micro batch size: 24
=> calculated gradient accumulation steps: 8
Found 97 .arrow files in sample-10BT/train
Total: 97 .arrow files for split train
Using streaming approach - no pre-processing
Loading file: /s2_nfs/fineweb-edu/localds/sample-10BT/train/data-00023-of-00097.arrow
Found 16 .arrow files in sample-10BT/test
Total: 16 .arrow files for split test
Using streaming approach - no pre-processing
Loading file: /s2_nfs/fineweb-edu/localds/sample-10BT/test/data-00000-of-00016.arrow
Regular params (lr=6.00e-04): decay=126,999,552, no_decay=38,400
Embedding params (lr=6.00e-05): decay=38,682,624, no_decay=0
using fused AdamW: True
### starting training loop of Encoder-<PERSON><PERSON> on Pile 20B @2025-09-16 14:59:29.682078
Total trainable parameters: 165_720_576
torch.compile: True
=> Batch_size:24; Sequence_length:1024; max_lr:0.0002; min_lr:2e-05
=> Target tokens: 20B; Total steps: 20000; Warmup steps: 400
Step 0: validation loss (MLM): 10.9698
step     0 | loss: 10.960980 | lr 5.0000e-07 | norm: 32.1294 | dt: 34367.76ms | tok/sec: 45765.68...
step     1 | loss: 10.926884 | lr 1.0000e-06 | norm: 30.4849 | dt: 1923.34ms | tok/sec: 817775.75...
step     2 | loss: 10.851862 | lr 1.5000e-06 | norm: 29.7828 | dt: 1775.17ms | tok/sec: 886037.80...
step     3 | loss: 10.740145 | lr 2.0000e-06 | norm: 26.1321 | dt: 1754.19ms | tok/sec: 896634.60...
step     4 | loss: 10.637581 | lr 2.5000e-06 | norm: 20.8523 | dt: 1850.12ms | tok/sec: 850140.27...
step     5 | loss: 10.531404 | lr 3.0000e-06 | norm: 17.2613 | dt: 1070.57ms | tok/sec: 1469180.77...
step     6 | loss: 10.441063 | lr 3.5000e-06 | norm: 15.0617 | dt: 1956.59ms | tok/sec: 803880.15...
step     7 | loss: 10.385352 | lr 4.0000e-06 | norm: 14.7072 | dt: 1956.06ms | tok/sec: 804098.36...
step     8 | loss: 10.251609 | lr 4.5000e-06 | norm: 12.7503 | dt: 1933.22ms | tok/sec: 813596.07...
step     9 | loss: 10.178262 | lr 5.0000e-06 | norm: 11.2826 | dt: 1876.70ms | tok/sec: 838102.45...
step    10 | loss: 10.120964 | lr 5.5000e-06 | norm: 9.9179 | dt: 1080.70ms | tok/sec: 1455414.27...
step    11 | loss: 10.042629 | lr 6.0000e-06 | norm: 9.5646 | dt: 2007.31ms | tok/sec: 783566.51...
step    12 | loss: 9.978855 | lr 6.5000e-06 | norm: 8.4911 | dt: 1933.24ms | tok/sec: 813591.55...
step    13 | loss: 9.981653 | lr 7.0000e-06 | norm: 8.2076 | dt: 2069.65ms | tok/sec: 759964.98...
step    14 | loss: 9.901602 | lr 7.5000e-06 | norm: 6.5472 | dt: 1812.08ms | tok/sec: 867989.13...
step    15 | loss: 9.851389 | lr 8.0000e-06 | norm: 5.9813 | dt: 1095.65ms | tok/sec: 1435552.13...
step    16 | loss: 9.791130 | lr 8.5000e-06 | norm: 5.4618 | dt: 1926.27ms | tok/sec: 816532.80...
step    17 | loss: 9.744937 | lr 9.0000e-06 | norm: 4.8211 | dt: 1918.25ms | tok/sec: 819947.41...
step    18 | loss: 9.730808 | lr 9.5000e-06 | norm: 4.1997 | dt: 1982.85ms | tok/sec: 793233.76...
step    19 | loss: 9.704381 | lr 1.0000e-05 | norm: 3.9038 | dt: 1728.96ms | tok/sec: 909714.92...
step    20 | loss: 9.720374 | lr 1.0500e-05 | norm: 3.6869 | dt: 1070.12ms | tok/sec: 1469796.47...
step    21 | loss: 9.664661 | lr 1.1000e-05 | norm: 3.3295 | dt: 1791.85ms | tok/sec: 877789.00...
step    22 | loss: 9.637115 | lr 1.1500e-05 | norm: 3.1642 | dt: 1763.02ms | tok/sec: 892144.41...
step    23 | loss: 9.666233 | lr 1.2000e-05 | norm: 2.9420 | dt: 1712.92ms | tok/sec: 918233.13...
step    24 | loss: 9.607037 | lr 1.2500e-05 | norm: 3.0119 | dt: 1740.45ms | tok/sec: 903712.77...
step    25 | loss: 9.624346 | lr 1.3000e-05 | norm: 2.9474 | dt: 1051.21ms | tok/sec: 1496248.03...
step    26 | loss: 9.619601 | lr 1.3500e-05 | norm: 2.7483 | dt: 1712.23ms | tok/sec: 918607.63...
step    27 | loss: 9.590935 | lr 1.4000e-05 | norm: 2.6061 | dt: 1681.35ms | tok/sec: 935475.26...
step    28 | loss: 9.571463 | lr 1.4500e-05 | norm: 2.6734 | dt: 1761.15ms | tok/sec: 893090.81...
step    29 | loss: 9.569848 | lr 1.5000e-05 | norm: 2.4630 | dt: 1743.59ms | tok/sec: 902085.67...
step    30 | loss: 9.559803 | lr 1.5500e-05 | norm: 2.4824 | dt: 1067.02ms | tok/sec: 1474077.07...
step    31 | loss: 9.545503 | lr 1.6000e-05 | norm: 2.3495 | dt: 1909.54ms | tok/sec: 823687.72...
step    32 | loss: 9.533940 | lr 1.6500e-05 | norm: 2.2911 | dt: 1871.10ms | tok/sec: 840609.61...
step    33 | loss: 9.505846 | lr 1.7000e-05 | norm: 2.4943 | dt: 1712.97ms | tok/sec: 918208.84...
step    34 | loss: 9.479873 | lr 1.7500e-05 | norm: 2.4058 | dt: 1896.33ms | tok/sec: 829426.15...
step    35 | loss: 9.436211 | lr 1.8000e-05 | norm: 2.9020 | dt: 1101.67ms | tok/sec: 1427704.48...
step    36 | loss: 9.438830 | lr 1.8500e-05 | norm: 2.3621 | dt: 2155.81ms | tok/sec: 729594.22...
step    37 | loss: 9.439543 | lr 1.9000e-05 | norm: 3.0551 | dt: 1720.29ms | tok/sec: 914300.17...
step    38 | loss: 9.383713 | lr 1.9500e-05 | norm: 2.3403 | dt: 1699.57ms | tok/sec: 925446.42...
step    39 | loss: 9.406484 | lr 2.0000e-05 | norm: 5.0938 | dt: 1657.22ms | tok/sec: 949098.09...
step    40 | loss: 9.342681 | lr 2.0500e-05 | norm: 3.2001 | dt: 1053.57ms | tok/sec: 1492885.45...
step    41 | loss: 9.350036 | lr 2.1000e-05 | norm: 5.4607 | dt: 1745.31ms | tok/sec: 901196.56...
step    42 | loss: 9.358878 | lr 2.1500e-05 | norm: 6.0220 | dt: 1761.13ms | tok/sec: 893099.51...
step    43 | loss: 9.282379 | lr 2.2000e-05 | norm: 3.3335 | dt: 1911.54ms | tok/sec: 822826.90...
step    44 | loss: 9.341360 | lr 2.2500e-05 | norm: 3.6970 | dt: 1835.00ms | tok/sec: 857148.58...
step    45 | loss: 9.259178 | lr 2.3000e-05 | norm: 2.5289 | dt: 1123.23ms | tok/sec: 1400308.96...
step    46 | loss: 9.241093 | lr 2.3500e-05 | norm: 2.7202 | dt: 1880.05ms | tok/sec: 836606.50...
step    47 | loss: 9.238214 | lr 2.4000e-05 | norm: 2.7897 | dt: 1905.52ms | tok/sec: 825427.16...
step    48 | loss: 9.193901 | lr 2.4500e-05 | norm: 2.4871 | dt: 1810.88ms | tok/sec: 868561.32...
step    49 | loss: 9.195190 | lr 2.5000e-05 | norm: 3.6192 | dt: 1888.02ms | tok/sec: 833077.69...
step    50 | loss: 9.174734 | lr 2.5500e-05 | norm: 2.5938 | dt: 1086.85ms | tok/sec: 1447181.88...
step    51 | loss: 9.144639 | lr 2.6000e-05 | norm: 5.2466 | dt: 1970.12ms | tok/sec: 798358.54...
step    52 | loss: 9.108422 | lr 2.6500e-05 | norm: 3.7784 | dt: 1989.78ms | tok/sec: 790472.37...
step    53 | loss: 9.076477 | lr 2.7000e-05 | norm: 5.2386 | dt: 1961.55ms | tok/sec: 801846.93...
step    54 | loss: 9.071864 | lr 2.7500e-05 | norm: 4.8754 | dt: 1789.46ms | tok/sec: 878958.52...
step    55 | loss: 9.047811 | lr 2.8000e-05 | norm: 3.2144 | dt: 1106.80ms | tok/sec: 1421097.15...
step    56 | loss: 9.035503 | lr 2.8500e-05 | norm: 3.3004 | dt: 1715.40ms | tok/sec: 916908.15...
step    57 | loss: 8.959921 | lr 2.9000e-05 | norm: 3.2030 | dt: 1733.21ms | tok/sec: 907485.94...
step    58 | loss: 8.860895 | lr 2.9500e-05 | norm: 2.8424 | dt: 1839.91ms | tok/sec: 854859.19...
step    59 | loss: 8.896460 | lr 3.0000e-05 | norm: 3.4613 | dt: 1708.96ms | tok/sec: 920363.87...
step    60 | loss: 8.925531 | lr 3.0500e-05 | norm: 3.3705 | dt: 1095.13ms | tok/sec: 1436238.76...
step    61 | loss: 8.898539 | lr 3.1000e-05 | norm: 2.3798 | dt: 1700.30ms | tok/sec: 925051.66...
step    62 | loss: 8.868132 | lr 3.1500e-05 | norm: 3.9686 | dt: 1677.78ms | tok/sec: 937467.28...
step    63 | loss: 8.803566 | lr 3.2000e-05 | norm: 2.4504 | dt: 1746.41ms | tok/sec: 900628.04...
step    64 | loss: 8.804857 | lr 3.2500e-05 | norm: 2.1314 | dt: 1737.27ms | tok/sec: 905366.99...
step    65 | loss: 8.827881 | lr 3.3000e-05 | norm: 2.4331 | dt: 1066.32ms | tok/sec: 1475035.51...
step    66 | loss: 8.704505 | lr 3.3500e-05 | norm: 2.7070 | dt: 1680.71ms | tok/sec: 935833.03...
step    67 | loss: 8.683824 | lr 3.4000e-05 | norm: 2.2690 | dt: 1807.87ms | tok/sec: 870010.30...
step    68 | loss: 8.680312 | lr 3.4500e-05 | norm: 2.1169 | dt: 1754.88ms | tok/sec: 896281.33...
step    69 | loss: 8.674170 | lr 3.5000e-05 | norm: 2.3860 | dt: 1685.52ms | tok/sec: 933162.77...
step    70 | loss: 8.647765 | lr 3.5500e-05 | norm: 2.1218 | dt: 1073.64ms | tok/sec: 1464989.36...
step    71 | loss: 8.613461 | lr 3.6000e-05 | norm: 2.5510 | dt: 1768.12ms | tok/sec: 889566.16...
step    72 | loss: 8.459793 | lr 3.6500e-05 | norm: 2.2888 | dt: 1708.86ms | tok/sec: 920415.75...
step    73 | loss: 8.472510 | lr 3.7000e-05 | norm: 2.1175 | dt: 1766.09ms | tok/sec: 890593.41...
step    74 | loss: 8.529692 | lr 3.7500e-05 | norm: 2.4314 | dt: 1704.46ms | tok/sec: 922795.52...
step    75 | loss: 8.432358 | lr 3.8000e-05 | norm: 2.3355 | dt: 1833.68ms | tok/sec: 857763.77...
step    76 | loss: 8.451019 | lr 3.8500e-05 | norm: 3.0091 | dt: 1636.27ms | tok/sec: 961252.45...
step    77 | loss: 8.402910 | lr 3.9000e-05 | norm: 2.0999 | dt: 1727.55ms | tok/sec: 910461.44...
step    78 | loss: 8.404970 | lr 3.9500e-05 | norm: 3.1965 | dt: 1716.68ms | tok/sec: 916225.46...
step    79 | loss: 8.360602 | lr 4.0000e-05 | norm: 2.6720 | dt: 1654.37ms | tok/sec: 950731.22...
step    80 | loss: 8.326174 | lr 4.0500e-05 | norm: 1.7942 | dt: 1081.40ms | tok/sec: 1454468.32...
step    81 | loss: 8.280106 | lr 4.1000e-05 | norm: 2.8352 | dt: 1709.17ms | tok/sec: 920252.82...
step    82 | loss: 8.265360 | lr 4.1500e-05 | norm: 2.0392 | dt: 1944.58ms | tok/sec: 808846.45...
step    83 | loss: 8.255198 | lr 4.2000e-05 | norm: 1.8225 | dt: 1884.92ms | tok/sec: 834445.44...
step    84 | loss: 8.219131 | lr 4.2500e-05 | norm: 2.2691 | dt: 1876.47ms | tok/sec: 838205.42...
step    85 | loss: 8.151842 | lr 4.3000e-05 | norm: 1.7882 | dt: 1113.81ms | tok/sec: 1412144.39...
step    86 | loss: 8.128977 | lr 4.3500e-05 | norm: 2.3202 | dt: 1907.79ms | tok/sec: 824444.51...
step    87 | loss: 8.089524 | lr 4.4000e-05 | norm: 2.0270 | dt: 1898.61ms | tok/sec: 828427.09...
step    88 | loss: 8.064266 | lr 4.4500e-05 | norm: 2.0923 | dt: 1957.10ms | tok/sec: 803672.24...
step    89 | loss: 8.063309 | lr 4.5000e-05 | norm: 2.4928 | dt: 1942.84ms | tok/sec: 809568.16...
step    90 | loss: 8.018386 | lr 4.5500e-05 | norm: 2.1408 | dt: 1078.96ms | tok/sec: 1457765.20...
step    91 | loss: 8.009054 | lr 4.6000e-05 | norm: 1.9903 | dt: 1879.98ms | tok/sec: 836636.63...
step    92 | loss: 7.917091 | lr 4.6500e-05 | norm: 2.3179 | dt: 1778.09ms | tok/sec: 884578.75...
step    93 | loss: 7.930045 | lr 4.7000e-05 | norm: 2.3940 | dt: 1742.43ms | tok/sec: 902683.70...
step    94 | loss: 7.905254 | lr 4.7500e-05 | norm: 1.7286 | dt: 1662.59ms | tok/sec: 946035.23...
step    95 | loss: 7.865256 | lr 4.8000e-05 | norm: 1.7592 | dt: 1146.14ms | tok/sec: 1372318.19...
step    96 | loss: 7.836675 | lr 4.8500e-05 | norm: 2.0918 | dt: 1797.55ms | tok/sec: 875005.49...
step    97 | loss: 7.823691 | lr 4.9000e-05 | norm: 1.6206 | dt: 1762.11ms | tok/sec: 892600.33...
step    98 | loss: 7.814138 | lr 4.9500e-05 | norm: 2.0727 | dt: 1737.80ms | tok/sec: 905088.63...
step    99 | loss: 7.746061 | lr 5.0000e-05 | norm: 2.6461 | dt: 1750.92ms | tok/sec: 898308.74...
Traceback (most recent call last):
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/train.py", line 348, in <module>
    main()
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/train.py", line 266, in main
    x, labels, attention_mask = train_loader.next_batch()
                                ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/data/dataloader_streaming.py", line 264, in next_batch
    sequence_tokens = self._create_sequence_tokens()
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/data/dataloader_streaming.py", line 232, in _create_sequence_tokens
    content_tokens = self._get_next_tokens(content_length)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/data/dataloader_streaming.py", line 219, in _get_next_tokens
    new_tokens = self.tokenizer.encode(text, add_special_tokens=False)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/transformers/tokenization_utils_base.py", line 2704, in encode
    encoded_inputs = self.encode_plus(
                     ^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/transformers/tokenization_utils_base.py", line 3095, in encode_plus
    return self._encode_plus(
           ^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/transformers/tokenization_utils_fast.py", line 627, in _encode_plus
    batched_output = self._batch_encode_plus(
                     ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/transformers/tokenization_utils_fast.py", line 553, in _batch_encode_plus
    encodings = self._tokenizer.encode_batch(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyboardInterrupt
[rank0]: Traceback (most recent call last):
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/train.py", line 348, in <module>
[rank0]:     main()
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/train.py", line 266, in main
[rank0]:     x, labels, attention_mask = train_loader.next_batch()
[rank0]:                                 ^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/data/dataloader_streaming.py", line 264, in next_batch
[rank0]:     sequence_tokens = self._create_sequence_tokens()
[rank0]:                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/data/dataloader_streaming.py", line 232, in _create_sequence_tokens
[rank0]:     content_tokens = self._get_next_tokens(content_length)
[rank0]:                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/data/dataloader_streaming.py", line 219, in _get_next_tokens
[rank0]:     new_tokens = self.tokenizer.encode(text, add_special_tokens=False)
[rank0]:                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/transformers/tokenization_utils_base.py", line 2704, in encode
[rank0]:     encoded_inputs = self.encode_plus(
[rank0]:                      ^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/transformers/tokenization_utils_base.py", line 3095, in encode_plus
[rank0]:     return self._encode_plus(
[rank0]:            ^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/transformers/tokenization_utils_fast.py", line 627, in _encode_plus
[rank0]:     batched_output = self._batch_encode_plus(
[rank0]:                      ^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/transformers/tokenization_utils_fast.py", line 553, in _batch_encode_plus
[rank0]:     encodings = self._tokenizer.encode_batch(
[rank0]:                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]: KeyboardInterrupt
