2025-09-16 15:24:30,908 INFO    MainThread:217540 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-09-16 15:24:30,908 INFO    MainThread:217540 [wandb_setup.py:_flush():80] Configure stats pid to 217540
2025-09-16 15:24:30,908 INFO    MainThread:217540 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-09-16 15:24:30,908 INFO    MainThread:217540 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/notebooks/Ali_embedding/Ali_embedding/wandb/settings
2025-09-16 15:24:30,908 INFO    MainThread:217540 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-09-16 15:24:30,908 INFO    MainThread:217540 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/notebooks/Ali_embedding/Ali_embedding/wandb/run-20250916_152430-5wwkl760/logs/debug.log
2025-09-16 15:24:30,908 INFO    MainThread:217540 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/notebooks/Ali_embedding/Ali_embedding/wandb/run-20250916_152430-5wwkl760/logs/debug-internal.log
2025-09-16 15:24:30,908 INFO    MainThread:217540 [wandb_init.py:init():830] calling init triggers
2025-09-16 15:24:30,908 INFO    MainThread:217540 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'model': {'n_layer': 12, 'n_head': 12, 'n_embd': 768, 'block_size': 1024, 'vocab_size': 50368, 'mask_prob': 0.15}, 'training': {'total_batch_size': 1572864, 'micro_batch_size': 24, 'sequence_length': 1024, 'max_steps': 20000, 'warmup_steps': 400, 'max_lr': 0.0002, 'min_lr': 2e-05, 'weight_decay': 0.1, 'optimizer_lr': 0.0006, 'embedding_lr_scale': 0.1}, 'data': {'dataset_name': 'Pile 20B', 'target_tokens': 20000000000, 'tokenizer_path': '/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls'}, '_wandb': {}}
2025-09-16 15:24:30,908 INFO    MainThread:217540 [wandb_init.py:init():871] starting backend
2025-09-16 15:24:31,113 INFO    MainThread:217540 [wandb_init.py:init():874] sending inform_init request
2025-09-16 15:24:31,116 INFO    MainThread:217540 [wandb_init.py:init():882] backend started and connected
2025-09-16 15:24:31,118 INFO    MainThread:217540 [wandb_init.py:init():953] updated telemetry
2025-09-16 15:24:31,118 INFO    MainThread:217540 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-09-16 15:24:32,874 INFO    MainThread:217540 [wandb_init.py:init():1029] starting run threads in backend
2025-09-16 15:24:32,979 INFO    MainThread:217540 [wandb_run.py:_console_start():2458] atexit reg
2025-09-16 15:24:32,980 INFO    MainThread:217540 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-09-16 15:24:32,980 INFO    MainThread:217540 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-09-16 15:24:32,980 INFO    MainThread:217540 [wandb_run.py:_redirect():2398] Redirects installed.
2025-09-16 15:24:32,982 INFO    MainThread:217540 [wandb_init.py:init():1075] run started, returning control to user process
2025-09-16 15:28:11,363 INFO    MsgRouterThr:217540 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
