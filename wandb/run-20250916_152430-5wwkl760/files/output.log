Wandb initialized: https://wandb.ai/axiomlaborg-axiom-lab/encoder-mlm-ali/runs/5wwkl760
Tokenizer vocab size: 50368
total desired batch size: 1572864
micro batch size: 24
=> calculated gradient accumulation steps: 8
Found 97 .arrow files in sample-10BT/train
Total: 97 .arrow files for split train
Using streaming approach - no pre-processing
Loading file: /s2_nfs/fineweb-edu/localds/sample-10BT/train/data-00023-of-00097.arrow
Found 16 .arrow files in sample-10BT/test
Total: 16 .arrow files for split test
Using streaming approach - no pre-processing
Loading file: /s2_nfs/fineweb-edu/localds/sample-10BT/test/data-00000-of-00016.arrow
Regular params (lr=6.00e-04): decay=126,999,552, no_decay=38,400
Embedding params (lr=6.00e-05): decay=38,682,624, no_decay=0
using fused AdamW: True
### starting training loop of Encoder-<PERSON><PERSON> on Pile 20B @2025-09-16 15:24:40.387977
Total trainable parameters: 165_720_576
torch.compile: True
=> Batch_size:24; Sequence_length:1024; max_lr:0.0002; min_lr:2e-05
=> Target tokens: 20B; Total steps: 20000; Warmup steps: 400
Step 0: validation loss (MLM): 10.9698
step     0 | loss: 10.960980 | lr 5.0000e-07 | norm: 32.1292 | dt: 32803.61ms | tok/sec: 47947.89...
step     1 | loss: 10.926896 | lr 1.0000e-06 | norm: 30.4844 | dt: 1123.59ms | tok/sec: 1399861.47...
step     2 | loss: 10.851858 | lr 1.5000e-06 | norm: 29.7826 | dt: 1084.23ms | tok/sec: 1450671.59...
step     3 | loss: 10.740141 | lr 2.0000e-06 | norm: 26.1322 | dt: 1076.50ms | tok/sec: 1461092.27...
step     4 | loss: 10.637582 | lr 2.5000e-06 | norm: 20.8526 | dt: 1078.26ms | tok/sec: 1458708.67...
step     5 | loss: 10.531404 | lr 3.0000e-06 | norm: 17.2617 | dt: 1120.07ms | tok/sec: 1404260.20...
step     6 | loss: 10.441063 | lr 3.5000e-06 | norm: 15.0620 | dt: 1096.64ms | tok/sec: 1434256.29...
step     7 | loss: 10.385357 | lr 4.0000e-06 | norm: 14.7066 | dt: 1103.63ms | tok/sec: 1425177.52...
step     8 | loss: 10.251609 | lr 4.5000e-06 | norm: 12.7501 | dt: 1125.25ms | tok/sec: 1397790.00...
step     9 | loss: 10.178266 | lr 5.0000e-06 | norm: 11.2834 | dt: 1110.41ms | tok/sec: 1416477.51...
step    10 | loss: 10.120947 | lr 5.5000e-06 | norm: 9.9171 | dt: 1116.55ms | tok/sec: 1408680.34...
step    11 | loss: 10.042630 | lr 6.0000e-06 | norm: 9.5644 | dt: 1096.96ms | tok/sec: 1433844.81...
step    12 | loss: 9.978849 | lr 6.5000e-06 | norm: 8.4914 | dt: 1410.94ms | tok/sec: 1114763.07...
step    13 | loss: 9.981663 | lr 7.0000e-06 | norm: 8.2071 | dt: 1306.77ms | tok/sec: 1203623.01...
step    14 | loss: 9.901596 | lr 7.5000e-06 | norm: 6.5471 | dt: 1097.52ms | tok/sec: 1433112.83...
step    15 | loss: 9.851395 | lr 8.0000e-06 | norm: 5.9813 | dt: 1134.85ms | tok/sec: 1385967.56...
step    16 | loss: 9.791137 | lr 8.5000e-06 | norm: 5.4618 | dt: 1080.32ms | tok/sec: 1455918.23...
step    17 | loss: 9.744938 | lr 9.0000e-06 | norm: 4.8216 | dt: 1065.63ms | tok/sec: 1475988.26...
step    18 | loss: 9.730794 | lr 9.5000e-06 | norm: 4.1993 | dt: 1039.68ms | tok/sec: 1512833.32...
step    19 | loss: 9.704374 | lr 1.0000e-05 | norm: 3.9036 | dt: 1076.60ms | tok/sec: 1460953.46...
step    20 | loss: 9.720379 | lr 1.0500e-05 | norm: 3.6869 | dt: 1869.85ms | tok/sec: 841172.97...
step    21 | loss: 9.664654 | lr 1.1000e-05 | norm: 3.3295 | dt: 1091.62ms | tok/sec: 1440850.57...
step    22 | loss: 9.637126 | lr 1.1500e-05 | norm: 3.1639 | dt: 2039.45ms | tok/sec: 771219.36...
step    23 | loss: 9.666232 | lr 1.2000e-05 | norm: 2.9414 | dt: 1182.15ms | tok/sec: 1330508.26...
step    24 | loss: 9.607038 | lr 1.2500e-05 | norm: 3.0118 | dt: 1041.69ms | tok/sec: 1509911.31...
step    25 | loss: 9.624351 | lr 1.3000e-05 | norm: 2.9479 | dt: 1084.81ms | tok/sec: 1449897.80...
step    26 | loss: 9.619596 | lr 1.3500e-05 | norm: 2.7480 | dt: 1092.53ms | tok/sec: 1439654.47...
step    27 | loss: 9.590940 | lr 1.4000e-05 | norm: 2.6063 | dt: 1703.22ms | tok/sec: 923465.28...
step    28 | loss: 9.571465 | lr 1.4500e-05 | norm: 2.6733 | dt: 1833.10ms | tok/sec: 858037.22...
step    29 | loss: 9.569841 | lr 1.5000e-05 | norm: 2.4629 | dt: 1783.84ms | tok/sec: 881728.15...
step    30 | loss: 9.559806 | lr 1.5500e-05 | norm: 2.4826 | dt: 1062.05ms | tok/sec: 1480972.00...
step    31 | loss: 9.545504 | lr 1.6000e-05 | norm: 2.3497 | dt: 1107.15ms | tok/sec: 1420637.19...
step    32 | loss: 9.533931 | lr 1.6500e-05 | norm: 2.2912 | dt: 1108.17ms | tok/sec: 1419328.74...
step    33 | loss: 9.505836 | lr 1.7000e-05 | norm: 2.4946 | dt: 1107.17ms | tok/sec: 1420613.03...
step    34 | loss: 9.479878 | lr 1.7500e-05 | norm: 2.4057 | dt: 1077.56ms | tok/sec: 1459656.27...
step    35 | loss: 9.436207 | lr 1.8000e-05 | norm: 2.9009 | dt: 1179.33ms | tok/sec: 1333691.39...
step    36 | loss: 9.438823 | lr 1.8500e-05 | norm: 2.3620 | dt: 1079.39ms | tok/sec: 1457175.31...
step    37 | loss: 9.439536 | lr 1.9000e-05 | norm: 3.0544 | dt: 1126.11ms | tok/sec: 1396717.22...
step    38 | loss: 9.383716 | lr 1.9500e-05 | norm: 2.3397 | dt: 1089.52ms | tok/sec: 1443632.15...
step    39 | loss: 9.406465 | lr 2.0000e-05 | norm: 5.0898 | dt: 1072.75ms | tok/sec: 1466200.90...
step    40 | loss: 9.342646 | lr 2.0500e-05 | norm: 3.1889 | dt: 1074.62ms | tok/sec: 1463651.54...
step    41 | loss: 9.350046 | lr 2.1000e-05 | norm: 5.4677 | dt: 1112.98ms | tok/sec: 1413202.55...
step    42 | loss: 9.358854 | lr 2.1500e-05 | norm: 6.0197 | dt: 1210.48ms | tok/sec: 1299371.67...
step    43 | loss: 9.282394 | lr 2.2000e-05 | norm: 3.3482 | dt: 1121.12ms | tok/sec: 1402933.98...
step    44 | loss: 9.341376 | lr 2.2500e-05 | norm: 3.6982 | dt: 1119.56ms | tok/sec: 1404897.77...
step    45 | loss: 9.259180 | lr 2.3000e-05 | norm: 2.5355 | dt: 1040.95ms | tok/sec: 1510982.68...
step    46 | loss: 9.241103 | lr 2.3500e-05 | norm: 2.7217 | dt: 1024.77ms | tok/sec: 1534842.26...
step    47 | loss: 9.238229 | lr 2.4000e-05 | norm: 2.7971 | dt: 1057.06ms | tok/sec: 1487960.61...
step    48 | loss: 9.193928 | lr 2.4500e-05 | norm: 2.4927 | dt: 1139.13ms | tok/sec: 1380762.91...
step    49 | loss: 9.195235 | lr 2.5000e-05 | norm: 3.6238 | dt: 1076.47ms | tok/sec: 1461136.93...
step    50 | loss: 9.174796 | lr 2.5500e-05 | norm: 2.6091 | dt: 1110.08ms | tok/sec: 1416896.73...
step    51 | loss: 9.144686 | lr 2.6000e-05 | norm: 5.2402 | dt: 1080.10ms | tok/sec: 1456223.85...
step    52 | loss: 9.108520 | lr 2.6500e-05 | norm: 3.8046 | dt: 1067.39ms | tok/sec: 1473560.79...
step    53 | loss: 9.076368 | lr 2.7000e-05 | norm: 5.1893 | dt: 1064.60ms | tok/sec: 1477428.14...
step    54 | loss: 9.071791 | lr 2.7500e-05 | norm: 4.8374 | dt: 1131.57ms | tok/sec: 1389988.39...
step    55 | loss: 9.047870 | lr 2.8000e-05 | norm: 3.2263 | dt: 1137.11ms | tok/sec: 1383206.03...
step    56 | loss: 9.035517 | lr 2.8500e-05 | norm: 3.2851 | dt: 1568.27ms | tok/sec: 1002930.28...
step    57 | loss: 8.960004 | lr 2.9000e-05 | norm: 3.2196 | dt: 1327.33ms | tok/sec: 1184984.35...
step    58 | loss: 8.860988 | lr 2.9500e-05 | norm: 2.8523 | dt: 1071.19ms | tok/sec: 1468337.11...
step    59 | loss: 8.896505 | lr 3.0000e-05 | norm: 3.4694 | dt: 1132.81ms | tok/sec: 1388462.76...
step    60 | loss: 8.925632 | lr 3.0500e-05 | norm: 3.3832 | dt: 1066.93ms | tok/sec: 1474192.69...
step    61 | loss: 8.898589 | lr 3.1000e-05 | norm: 2.3811 | dt: 1134.73ms | tok/sec: 1386114.91...
step    62 | loss: 8.868441 | lr 3.1500e-05 | norm: 4.0246 | dt: 1127.46ms | tok/sec: 1395055.54...
step    63 | loss: 8.803537 | lr 3.2000e-05 | norm: 2.3898 | dt: 1098.63ms | tok/sec: 1431658.56...
step    64 | loss: 8.804976 | lr 3.2500e-05 | norm: 2.1890 | dt: 1169.34ms | tok/sec: 1345083.25...
step    65 | loss: 8.828564 | lr 3.3000e-05 | norm: 2.6274 | dt: 1123.18ms | tok/sec: 1400361.87...
step    66 | loss: 8.704519 | lr 3.3500e-05 | norm: 2.5941 | dt: 1054.77ms | tok/sec: 1491188.76...
step    67 | loss: 8.683757 | lr 3.4000e-05 | norm: 2.2320 | dt: 1142.24ms | tok/sec: 1376999.24...
step    68 | loss: 8.680847 | lr 3.4500e-05 | norm: 2.2920 | dt: 1080.66ms | tok/sec: 1455459.86...
step    69 | loss: 8.674717 | lr 3.5000e-05 | norm: 2.4569 | dt: 1036.71ms | tok/sec: 1517171.14...
step    70 | loss: 8.648573 | lr 3.5500e-05 | norm: 2.1736 | dt: 1095.92ms | tok/sec: 1435200.16...
step    71 | loss: 8.613563 | lr 3.6000e-05 | norm: 2.4257 | dt: 1064.02ms | tok/sec: 1478227.97...
step    72 | loss: 8.460327 | lr 3.6500e-05 | norm: 2.3089 | dt: 1104.95ms | tok/sec: 1423464.67...
step    73 | loss: 8.472904 | lr 3.7000e-05 | norm: 2.0855 | dt: 1066.51ms | tok/sec: 1474776.00...
step    74 | loss: 8.529549 | lr 3.7500e-05 | norm: 2.2366 | dt: 1180.00ms | tok/sec: 1332931.21...
step    75 | loss: 8.433197 | lr 3.8000e-05 | norm: 2.5244 | dt: 1095.11ms | tok/sec: 1436262.84...
step    76 | loss: 8.450313 | lr 3.8500e-05 | norm: 2.7412 | dt: 1075.22ms | tok/sec: 1462828.16...
step    77 | loss: 8.402740 | lr 3.9000e-05 | norm: 2.1356 | dt: 1085.29ms | tok/sec: 1449254.71...
step    78 | loss: 8.405614 | lr 3.9500e-05 | norm: 3.4088 | dt: 1081.97ms | tok/sec: 1453706.81...
step    79 | loss: 8.359320 | lr 4.0000e-05 | norm: 2.3905 | dt: 1089.75ms | tok/sec: 1443326.73...
step    80 | loss: 8.325338 | lr 4.0500e-05 | norm: 1.6914 | dt: 1072.12ms | tok/sec: 1467065.60...
step    81 | loss: 8.278327 | lr 4.1000e-05 | norm: 2.6314 | dt: 1051.52ms | tok/sec: 1495801.23...
step    82 | loss: 8.264462 | lr 4.1500e-05 | norm: 2.2696 | dt: 1100.44ms | tok/sec: 1429299.03...
step    83 | loss: 8.254291 | lr 4.2000e-05 | norm: 1.6540 | dt: 1091.57ms | tok/sec: 1440914.77...
step    84 | loss: 8.215771 | lr 4.2500e-05 | norm: 1.9292 | dt: 1058.68ms | tok/sec: 1485687.32...
step    85 | loss: 8.150345 | lr 4.3000e-05 | norm: 1.8945 | dt: 1091.19ms | tok/sec: 1441427.00...
step    86 | loss: 8.124773 | lr 4.3500e-05 | norm: 1.8794 | dt: 1123.47ms | tok/sec: 1400003.77...
step    87 | loss: 8.091063 | lr 4.4000e-05 | norm: 3.2804 | dt: 1058.93ms | tok/sec: 1485333.42...
step    88 | loss: 8.064217 | lr 4.4500e-05 | norm: 2.2198 | dt: 1062.86ms | tok/sec: 1479840.50...
step    89 | loss: 8.060201 | lr 4.5000e-05 | norm: 1.6592 | dt: 1116.94ms | tok/sec: 1408192.61...
step    90 | loss: 8.016592 | lr 4.5500e-05 | norm: 2.3765 | dt: 1070.14ms | tok/sec: 1469775.52...
step    91 | loss: 8.007118 | lr 4.6000e-05 | norm: 1.8185 | dt: 1105.75ms | tok/sec: 1422434.94...
step    92 | loss: 7.915312 | lr 4.6500e-05 | norm: 2.3194 | dt: 1060.15ms | tok/sec: 1483619.81...
step    93 | loss: 7.927870 | lr 4.7000e-05 | norm: 2.2898 | dt: 1096.54ms | tok/sec: 1434387.26...
step    94 | loss: 7.903921 | lr 4.7500e-05 | norm: 1.5960 | dt: 1115.39ms | tok/sec: 1410141.62...
step    95 | loss: 7.863540 | lr 4.8000e-05 | norm: 1.9845 | dt: 1104.57ms | tok/sec: 1423965.79...
step    96 | loss: 7.839229 | lr 4.8500e-05 | norm: 3.0671 | dt: 1136.19ms | tok/sec: 1384333.66...
step    97 | loss: 7.823125 | lr 4.9000e-05 | norm: 1.3956 | dt: 1138.92ms | tok/sec: 1381014.66...
step    98 | loss: 7.816805 | lr 4.9500e-05 | norm: 3.0897 | dt: 1134.36ms | tok/sec: 1386559.77...
step    99 | loss: 7.744546 | lr 5.0000e-05 | norm: 1.8569 | dt: 1150.29ms | tok/sec: 1367367.25...
step   100 | loss: 7.761632 | lr 5.0500e-05 | norm: 1.6323 | dt: 1223.03ms | tok/sec: 1286036.67...
step   101 | loss: 7.631560 | lr 5.1000e-05 | norm: 2.6032 | dt: 1093.96ms | tok/sec: 1437776.30...
step   102 | loss: 7.722440 | lr 5.1500e-05 | norm: 1.9516 | dt: 1108.86ms | tok/sec: 1418445.26...
step   103 | loss: 7.707808 | lr 5.2000e-05 | norm: 2.1363 | dt: 1108.47ms | tok/sec: 1418953.54...
step   104 | loss: 7.725162 | lr 5.2500e-05 | norm: 1.7382 | dt: 1085.61ms | tok/sec: 1448826.62...
step   105 | loss: 7.696410 | lr 5.3000e-05 | norm: 1.2475 | dt: 1063.14ms | tok/sec: 1479456.86...
step   106 | loss: 7.651374 | lr 5.3500e-05 | norm: 1.6488 | dt: 1068.05ms | tok/sec: 1472648.31...
step   107 | loss: 7.633491 | lr 5.4000e-05 | norm: 2.1395 | dt: 1101.07ms | tok/sec: 1428488.48...
step   108 | loss: 7.610048 | lr 5.4500e-05 | norm: 1.1666 | dt: 1096.27ms | tok/sec: 1434745.70...
step   109 | loss: 7.568660 | lr 5.5000e-05 | norm: 1.1973 | dt: 1125.33ms | tok/sec: 1397690.79...
step   110 | loss: 7.579385 | lr 5.5500e-05 | norm: 1.6054 | dt: 1059.57ms | tok/sec: 1484440.71...
step   111 | loss: 7.557394 | lr 5.6000e-05 | norm: 1.4602 | dt: 1078.34ms | tok/sec: 1458595.14...
step   112 | loss: 7.546806 | lr 5.6500e-05 | norm: 1.0628 | dt: 1171.12ms | tok/sec: 1343046.74...
step   113 | loss: 7.521749 | lr 5.7000e-05 | norm: 1.6603 | dt: 1123.68ms | tok/sec: 1399747.71...
step   114 | loss: 7.482175 | lr 5.7500e-05 | norm: 2.3832 | dt: 1079.41ms | tok/sec: 1457154.06...
step   115 | loss: 7.481977 | lr 5.8000e-05 | norm: 1.1380 | dt: 1114.40ms | tok/sec: 1411397.55...
step   116 | loss: 7.497112 | lr 5.8500e-05 | norm: 1.8466 | dt: 1146.48ms | tok/sec: 1371912.66...
step   117 | loss: 7.468211 | lr 5.9000e-05 | norm: 1.3409 | dt: 1101.67ms | tok/sec: 1427713.44...
step   118 | loss: 7.443648 | lr 5.9500e-05 | norm: 1.4133 | dt: 1096.36ms | tok/sec: 1434623.71...
step   119 | loss: 7.381595 | lr 6.0000e-05 | norm: 1.4761 | dt: 1140.56ms | tok/sec: 1379033.14...
step   120 | loss: 7.413947 | lr 6.0500e-05 | norm: 0.7854 | dt: 1081.09ms | tok/sec: 1454892.05...
step   121 | loss: 7.373559 | lr 6.1000e-05 | norm: 1.5108 | dt: 1105.82ms | tok/sec: 1422352.14...
step   122 | loss: 7.410598 | lr 6.1500e-05 | norm: 1.5797 | dt: 1091.56ms | tok/sec: 1440932.08...
step   123 | loss: 7.441329 | lr 6.2000e-05 | norm: 2.0614 | dt: 1145.10ms | tok/sec: 1373561.68...
step   124 | loss: 7.402565 | lr 6.2500e-05 | norm: 0.8761 | dt: 1125.79ms | tok/sec: 1397126.01...
step   125 | loss: 7.350463 | lr 6.3000e-05 | norm: 0.7547 | dt: 1093.90ms | tok/sec: 1437853.70...
step   126 | loss: 7.406192 | lr 6.3500e-05 | norm: 1.2564 | dt: 1037.02ms | tok/sec: 1516716.30...
step   127 | loss: 7.410110 | lr 6.4000e-05 | norm: 1.1582 | dt: 1101.25ms | tok/sec: 1428257.45...
step   128 | loss: 7.366209 | lr 6.4500e-05 | norm: 2.0616 | dt: 1065.28ms | tok/sec: 1476481.13...
step   129 | loss: 7.381577 | lr 6.5000e-05 | norm: 0.6823 | dt: 1126.61ms | tok/sec: 1396102.12...
step   130 | loss: 7.357485 | lr 6.5500e-05 | norm: 1.6626 | dt: 1115.07ms | tok/sec: 1410553.18...
step   131 | loss: 7.400703 | lr 6.6000e-05 | norm: 1.2082 | dt: 1106.07ms | tok/sec: 1422029.60...
step   132 | loss: 7.375107 | lr 6.6500e-05 | norm: 1.3223 | dt: 1100.32ms | tok/sec: 1429464.11...
step   133 | loss: 7.362695 | lr 6.7000e-05 | norm: 2.6406 | dt: 1029.39ms | tok/sec: 1527963.21...
step   134 | loss: 7.400898 | lr 6.7500e-05 | norm: 1.1206 | dt: 1119.25ms | tok/sec: 1405284.72...
step   135 | loss: 7.386532 | lr 6.8000e-05 | norm: 3.4862 | dt: 1023.13ms | tok/sec: 1537311.93...
step   136 | loss: 7.339468 | lr 6.8500e-05 | norm: 1.7569 | dt: 1096.03ms | tok/sec: 1435057.80...
step   137 | loss: 7.341093 | lr 6.9000e-05 | norm: 2.2884 | dt: 1093.73ms | tok/sec: 1438071.54...
step   138 | loss: 7.355862 | lr 6.9500e-05 | norm: 1.8651 | dt: 1147.54ms | tok/sec: 1370640.55...
step   139 | loss: 7.335459 | lr 7.0000e-05 | norm: 1.8663 | dt: 1070.68ms | tok/sec: 1469027.99...
step   140 | loss: 7.357287 | lr 7.0500e-05 | norm: 1.5580 | dt: 1108.73ms | tok/sec: 1418617.60...
step   141 | loss: 7.291749 | lr 7.1000e-05 | norm: 1.7123 | dt: 1110.44ms | tok/sec: 1416427.33...
step   142 | loss: 7.370156 | lr 7.1500e-05 | norm: 1.7272 | dt: 1194.50ms | tok/sec: 1316753.32...
step   143 | loss: 7.363151 | lr 7.2000e-05 | norm: 1.3485 | dt: 1102.54ms | tok/sec: 1426583.16...
step   144 | loss: 7.343730 | lr 7.2500e-05 | norm: 0.8908 | dt: 1068.77ms | tok/sec: 1471662.11...
step   145 | loss: 7.370226 | lr 7.3000e-05 | norm: 1.4413 | dt: 1101.70ms | tok/sec: 1427669.57...
step   146 | loss: 7.341944 | lr 7.3500e-05 | norm: 0.6778 | dt: 1143.94ms | tok/sec: 1374952.69...
step   147 | loss: 7.353566 | lr 7.4000e-05 | norm: 1.1877 | dt: 1151.26ms | tok/sec: 1366215.58...
step   148 | loss: 7.342306 | lr 7.4500e-05 | norm: 0.9120 | dt: 1232.57ms | tok/sec: 1276082.28...
step   149 | loss: 7.377985 | lr 7.5000e-05 | norm: 1.1535 | dt: 1319.91ms | tok/sec: 1191645.70...
step   150 | loss: 7.374876 | lr 7.5500e-05 | norm: 9.5494 | dt: 1017.43ms | tok/sec: 1545921.43...
step   151 | loss: 7.248064 | lr 7.6000e-05 | norm: 3.5395 | dt: 1100.66ms | tok/sec: 1429015.75...
step   152 | loss: 7.249653 | lr 7.6500e-05 | norm: 1.4351 | dt: 1005.67ms | tok/sec: 1564000.11...
step   153 | loss: 7.321715 | lr 7.7000e-05 | norm: 1.2249 | dt: 1060.03ms | tok/sec: 1483796.00...
step   154 | loss: 7.337661 | lr 7.7500e-05 | norm: 1.5029 | dt: 1095.07ms | tok/sec: 1436316.63...
step   155 | loss: 7.365250 | lr 7.8000e-05 | norm: 1.0380 | dt: 1063.91ms | tok/sec: 1478386.31...
Traceback (most recent call last):
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/train.py", line 348, in <module>
    main()
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/train.py", line 278, in main
    _, loss = model(x, attention_mask=attention_mask, labels=labels)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1637, in forward
    else self._run_ddp_forward(*inputs, **kwargs)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1464, in _run_ddp_forward
    return self.module(*inputs, **kwargs)  # type: ignore[index]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 655, in _fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/models/masked_language_model.py", line 71, in forward
    def forward(self, idx, attention_mask=None, labels=None):
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 838, in _fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/fx/graph_module.py", line 830, in call_wrapped
    return self._wrapped_call(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/fx/graph_module.py", line 393, in __call__
    return super(self.cls, obj).__call__(*args, **kwargs)  # type: ignore[misc]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<eval_with_key>.264", line 305, in forward
    submod_6 = self.compiled_submod_6(getitem_10, l_self_modules_transformer_modules_h_modules_5_modules_mlp_modules_c_proj_parameters_weight_, getitem_11, l_self_modules_transformer_modules_h_modules_6_modules_ln_1_parameters_weight_, l_self_modules_transformer_modules_h_modules_6_modules_ln_1_parameters_bias_, l_attention_mask_, l_self_modules_transformer_modules_h_modules_6_modules_attn_parameters_time_maa_x_, l_self_modules_transformer_modules_h_modules_6_modules_attn_parameters_time_maa_w1_, l_self_modules_transformer_modules_h_modules_6_modules_attn_parameters_time_maa_w2_, l_self_modules_transformer_modules_h_modules_6_modules_attn_parameters_time_maa_q_, l_self_modules_transformer_modules_h_modules_6_modules_attn_parameters_time_maa_k_, l_self_modules_transformer_modules_h_modules_6_modules_attn_parameters_time_maa_v_, l_self_modules_transformer_modules_h_modules_6_modules_attn_modules_query_parameters_weight_, l_self_modules_transformer_modules_h_modules_6_modules_attn_modules_key_parameters_weight_, l_self_modules_transformer_modules_h_modules_6_modules_attn_modules_value_parameters_weight_, l_self_modules_transformer_modules_h_modules_6_modules_attn_buffers_inv_freq_, l_self_modules_transformer_modules_h_modules_6_modules_attn_modules_c_proj_parameters_weight_, l_self_modules_transformer_modules_h_modules_6_modules_ln_2_parameters_weight_, l_self_modules_transformer_modules_h_modules_6_modules_ln_2_parameters_bias_, l_self_modules_transformer_modules_h_modules_6_modules_mlp_parameters_time_maa_x_, l_self_modules_transformer_modules_h_modules_6_modules_mlp_parameters_time_maa_w1_, l_self_modules_transformer_modules_h_modules_6_modules_mlp_parameters_time_maa_w2_, l_self_modules_transformer_modules_h_modules_6_modules_mlp_parameters_time_maa_gate_, l_self_modules_transformer_modules_h_modules_6_modules_mlp_parameters_time_maa_value_, l_self_modules_transformer_modules_h_modules_6_modules_mlp_modules_c_fc_gate_parameters_weight_, l_self_modules_transformer_modules_h_modules_6_modules_mlp_modules_c_fc_value_parameters_weight_);  getitem_10 = l_self_modules_transformer_modules_h_modules_5_modules_mlp_modules_c_proj_parameters_weight_ = getitem_11 = l_self_modules_transformer_modules_h_modules_6_modules_ln_1_parameters_weight_ = l_self_modules_transformer_modules_h_modules_6_modules_ln_1_parameters_bias_ = l_self_modules_transformer_modules_h_modules_6_modules_attn_parameters_time_maa_x_ = l_self_modules_transformer_modules_h_modules_6_modules_attn_parameters_time_maa_w1_ = l_self_modules_transformer_modules_h_modules_6_modules_attn_parameters_time_maa_w2_ = l_self_modules_transformer_modules_h_modules_6_modules_attn_parameters_time_maa_q_ = l_self_modules_transformer_modules_h_modules_6_modules_attn_parameters_time_maa_k_ = l_self_modules_transformer_modules_h_modules_6_modules_attn_parameters_time_maa_v_ = l_self_modules_transformer_modules_h_modules_6_modules_attn_modules_query_parameters_weight_ = l_self_modules_transformer_modules_h_modules_6_modules_attn_modules_key_parameters_weight_ = l_self_modules_transformer_modules_h_modules_6_modules_attn_modules_value_parameters_weight_ = l_self_modules_transformer_modules_h_modules_6_modules_attn_buffers_inv_freq_ = l_self_modules_transformer_modules_h_modules_6_modules_attn_modules_c_proj_parameters_weight_ = l_self_modules_transformer_modules_h_modules_6_modules_ln_2_parameters_weight_ = l_self_modules_transformer_modules_h_modules_6_modules_ln_2_parameters_bias_ = l_self_modules_transformer_modules_h_modules_6_modules_mlp_parameters_time_maa_x_ = l_self_modules_transformer_modules_h_modules_6_modules_mlp_parameters_time_maa_w1_ = l_self_modules_transformer_modules_h_modules_6_modules_mlp_parameters_time_maa_w2_ = l_self_modules_transformer_modules_h_modules_6_modules_mlp_parameters_time_maa_gate_ = l_self_modules_transformer_modules_h_modules_6_modules_mlp_parameters_time_maa_value_ = l_self_modules_transformer_modules_h_modules_6_modules_mlp_modules_c_fc_gate_parameters_weight_ = l_self_modules_transformer_modules_h_modules_6_modules_mlp_modules_c_fc_value_paramet
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/backends/distributed.py", line 171, in forward
    x = self.submod(*args)
        ^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 838, in _fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/aot_autograd.py", line 1201, in forward
    return compiled_fn(full_args)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 315, in runtime_wrapper
    all_outs = call_func_at_runtime_with_args(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/utils.py", line 126, in call_func_at_runtime_with_args
    out = normalize_as_list(f(args))
                            ^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/utils.py", line 100, in g
    return f(*args)
           ^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/autograd/function.py", line 575, in apply
    return super().apply(*args, **kwargs)  # type: ignore[misc]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 1937, in forward
    fw_outs = call_func_at_runtime_with_args(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/utils.py", line 126, in call_func_at_runtime_with_args
    out = normalize_as_list(f(args))
                            ^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 598, in wrapper
    return compiled_fn(runtime_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 495, in wrapper
    return compiled_fn(runtime_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 689, in inner_fn
    outs = compiled_fn(args)
           ^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_inductor/output_code.py", line 460, in __call__
    return self.current_callable(inputs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_inductor/utils.py", line 2404, in run
    return model(new_inputs)
           ^^^^^^^^^^^^^^^^^
  File "/tmp/torchinductor_modernbert/2e/c2ecj3tv2o3c5u2f5eybkwyq4dadz6ddippy7saf3ss62fj7tt2o.py", line 1437, in call
    extern_kernels.mm(reinterpret_tensor(buf20, (24576, 768), (768, 1), 0), buf19, out=buf21)
KeyboardInterrupt
[rank0]: Traceback (most recent call last):
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/train.py", line 348, in <module>
[rank0]:     main()
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/train.py", line 278, in main
[rank0]:     _, loss = model(x, attention_mask=attention_mask, labels=labels)
[rank0]:               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1637, in forward
[rank0]:     else self._run_ddp_forward(*inputs, **kwargs)
[rank0]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1464, in _run_ddp_forward
[rank0]:     return self.module(*inputs, **kwargs)  # type: ignore[index]
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 655, in _fn
[rank0]:     return fn(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/models/masked_language_model.py", line 71, in forward
[rank0]:     def forward(self, idx, attention_mask=None, labels=None):
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 838, in _fn
[rank0]:     return fn(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/fx/graph_module.py", line 830, in call_wrapped
[rank0]:     return self._wrapped_call(self, *args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/fx/graph_module.py", line 393, in __call__
[rank0]:     return super(self.cls, obj).__call__(*args, **kwargs)  # type: ignore[misc]
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "<eval_with_key>.264", line 305, in forward
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/backends/distributed.py", line 171, in forward
[rank0]:     x = self.submod(*args)
[rank0]:         ^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 838, in _fn
[rank0]:     return fn(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/aot_autograd.py", line 1201, in forward
[rank0]:     return compiled_fn(full_args)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 315, in runtime_wrapper
[rank0]:     all_outs = call_func_at_runtime_with_args(
[rank0]:                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/utils.py", line 126, in call_func_at_runtime_with_args
[rank0]:     out = normalize_as_list(f(args))
[rank0]:                             ^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/utils.py", line 100, in g
[rank0]:     return f(*args)
[rank0]:            ^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/autograd/function.py", line 575, in apply
[rank0]:     return super().apply(*args, **kwargs)  # type: ignore[misc]
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 1937, in forward
[rank0]:     fw_outs = call_func_at_runtime_with_args(
[rank0]:               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/utils.py", line 126, in call_func_at_runtime_with_args
[rank0]:     out = normalize_as_list(f(args))
[rank0]:                             ^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 598, in wrapper
[rank0]:     return compiled_fn(runtime_args)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 495, in wrapper
[rank0]:     return compiled_fn(runtime_args)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 689, in inner_fn
[rank0]:     outs = compiled_fn(args)
[rank0]:            ^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_inductor/output_code.py", line 460, in __call__
[rank0]:     return self.current_callable(inputs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_inductor/utils.py", line 2404, in run
[rank0]:     return model(new_inputs)
[rank0]:            ^^^^^^^^^^^^^^^^^
[rank0]:   File "/tmp/torchinductor_modernbert/2e/c2ecj3tv2o3c5u2f5eybkwyq4dadz6ddippy7saf3ss62fj7tt2o.py", line 1437, in call
[rank0]:     extern_kernels.mm(reinterpret_tensor(buf20, (24576, 768), (768, 1), 0), buf19, out=buf21)
[rank0]: KeyboardInterrupt
