Wandb initialized: https://wandb.ai/axiomlaborg-axiom-lab/encoder-mlm-ali/runs/lmzol3dd
Tokenizer vocab size: 50368
total desired batch size: 1048576
micro batch size: 32
=> calculated gradient accumulation steps: 4
Found 97 .arrow files in sample-10BT/train
Total: 97 .arrow files for split train
Using streaming approach - no pre-processing
Loading file: /s2_nfs/fineweb-edu/localds/sample-10BT/train/data-00023-of-00097.arrow
Found 16 .arrow files in sample-10BT/test
Total: 16 .arrow files for split test
Using streaming approach - no pre-processing
Loading file: /s2_nfs/fineweb-edu/localds/sample-10BT/test/data-00000-of-00016.arrow
Warning: tokenizer.cls_token_ids is not a list: 1, creating fallback
Regular params (lr=6.00e-04): decay=126,410,496, no_decay=38,400
Embedding params (lr=6.00e-05): decay=38,670,336, no_decay=0
Multi-CLS embedding params (lr=6.00e-05): 12,288
using fused AdamW: True
### starting training loop of Encoder-<PERSON><PERSON> on finweb 10B @2025-09-17 17:31:18.700903
Total trainable parameters: 165_131_520
torch.compile: True
=> Batch_size:32; Sequence_length:1024; max_lr:0.0002; min_lr:2e-05
=> Target tokens: 10B; Total steps: 20000; Warmup steps: 400
Traceback (most recent call last):
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/train.py", line 348, in <module>
    main()
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/train.py", line 222, in main
    _, loss = model(x, attention_mask=attention_mask, labels=labels)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1637, in forward
    else self._run_ddp_forward(*inputs, **kwargs)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1464, in _run_ddp_forward
    return self.module(*inputs, **kwargs)  # type: ignore[index]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 655, in _fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/models/masked_language_model.py", line 169, in forward
    if regular_mask.any():
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/models/masked_language_model.py", line 170, in torch_dynamo_resume_in_forward_at_169
    tok_emb[regular_mask] = self.transformer.wte(idx[regular_mask])
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/models/masked_language_model.py", line 170, in torch_dynamo_resume_in_forward_at_170
    tok_emb[regular_mask] = self.transformer.wte(idx[regular_mask])
    ~~~~~~~^^^^^^^^^^^^^^
RuntimeError: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

[rank0]: Traceback (most recent call last):
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/train.py", line 348, in <module>
[rank0]:     main()
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/train.py", line 222, in main
[rank0]:     _, loss = model(x, attention_mask=attention_mask, labels=labels)
[rank0]:               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1637, in forward
[rank0]:     else self._run_ddp_forward(*inputs, **kwargs)
[rank0]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1464, in _run_ddp_forward
[rank0]:     return self.module(*inputs, **kwargs)  # type: ignore[index]
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 655, in _fn
[rank0]:     return fn(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/models/masked_language_model.py", line 169, in forward
[rank0]:     if regular_mask.any():
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/models/masked_language_model.py", line 170, in torch_dynamo_resume_in_forward_at_169
[rank0]:     tok_emb[regular_mask] = self.transformer.wte(idx[regular_mask])
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/models/masked_language_model.py", line 170, in torch_dynamo_resume_in_forward_at_170
[rank0]:     tok_emb[regular_mask] = self.transformer.wte(idx[regular_mask])
[rank0]:     ~~~~~~~^^^^^^^^^^^^^^
[rank0]: RuntimeError: CUDA error: device-side assert triggered
[rank0]: CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
[rank0]: For debugging consider passing CUDA_LAUNCH_BLOCKING=1
[rank0]: Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.
