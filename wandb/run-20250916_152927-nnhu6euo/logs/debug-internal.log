{"time":"2025-09-16T15:29:27.687866228+03:30","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-16T15:29:28.707663793+03:30","level":"INFO","msg":"stream: created new stream","id":"nnhu6euo"}
{"time":"2025-09-16T15:29:28.707718228+03:30","level":"INFO","msg":"stream: started","id":"nnhu6euo"}
{"time":"2025-09-16T15:29:28.707738853+03:30","level":"INFO","msg":"writer: Do: started","stream_id":"nnhu6euo"}
{"time":"2025-09-16T15:29:28.707792268+03:30","level":"INFO","msg":"sender: started","stream_id":"nnhu6euo"}
{"time":"2025-09-16T15:29:28.707755433+03:30","level":"INFO","msg":"handler: started","stream_id":"nnhu6euo"}
{"time":"2025-09-16T15:30:51.898366519+03:30","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/axiomlaborg-axiom-lab/encoder-mlm-ali/nnhu6euo/file_stream\": write tcp [::1]:56946->[::1]:8118: write: broken pipe"}
{"time":"2025-09-16T15:30:54.203414541+03:30","level":"INFO","msg":"stream: closing","id":"nnhu6euo"}
{"time":"2025-09-16T15:30:55.011695721+03:30","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-09-16T15:30:55.899130833+03:30","level":"INFO","msg":"handler: closed","stream_id":"nnhu6euo"}
{"time":"2025-09-16T15:30:55.899175006+03:30","level":"INFO","msg":"sender: closed","stream_id":"nnhu6euo"}
{"time":"2025-09-16T15:30:55.899171392+03:30","level":"INFO","msg":"writer: Close: closed","stream_id":"nnhu6euo"}
{"time":"2025-09-16T15:30:55.899287242+03:30","level":"INFO","msg":"stream: closed","id":"nnhu6euo"}
