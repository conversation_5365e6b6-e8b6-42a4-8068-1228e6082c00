Wandb initialized: https://wandb.ai/axiomlaborg-axiom-lab/encoder-mlm-ali/runs/nnhu6euo
Tokenizer vocab size: 50368
total desired batch size: 2097152
micro batch size: 32
=> calculated gradient accumulation steps: 8
Found 97 .arrow files in sample-10BT/train
Total: 97 .arrow files for split train
Using streaming approach - no pre-processing
Loading file: /s2_nfs/fineweb-edu/localds/sample-10BT/train/data-00023-of-00097.arrow
Found 16 .arrow files in sample-10BT/test
Total: 16 .arrow files for split test
Using streaming approach - no pre-processing
Loading file: /s2_nfs/fineweb-edu/localds/sample-10BT/test/data-00000-of-00016.arrow
Regular params (lr=6.00e-04): decay=126,999,552, no_decay=38,400
Embedding params (lr=6.00e-05): decay=38,682,624, no_decay=0
using fused AdamW: True
### starting training loop of Encoder-<PERSON><PERSON> on finweb 10B @2025-09-16 15:29:40.951023
Total trainable parameters: 165_720_576
torch.compile: True
=> Batch_size:32; Sequence_length:1024; max_lr:0.0002; min_lr:2e-05
=> Target tokens: 10B; Total steps: 20000; Warmup steps: 400
Step 0: validation loss (MLM): 10.9694
step     0 | loss: 10.965168 | lr 5.0000e-07 | norm: 31.6601 | dt: 45392.40ms | tok/sec: 46200.51...
step     1 | loss: 10.928584 | lr 1.0000e-06 | norm: 31.0777 | dt: 1382.27ms | tok/sec: 1517184.31...
step     2 | loss: 10.846272 | lr 1.5000e-06 | norm: 29.6539 | dt: 1412.47ms | tok/sec: 1484738.30...
step     3 | loss: 10.748468 | lr 2.0000e-06 | norm: 25.5043 | dt: 1325.46ms | tok/sec: 1582209.83...
step     4 | loss: 10.640107 | lr 2.5000e-06 | norm: 20.9517 | dt: 1373.26ms | tok/sec: 1527138.72...
step     5 | loss: 10.555195 | lr 3.0000e-06 | norm: 17.7386 | dt: 1314.37ms | tok/sec: 1595556.33...
step     6 | loss: 10.423438 | lr 3.5000e-06 | norm: 15.7808 | dt: 1402.36ms | tok/sec: 1495442.83...
step     7 | loss: 10.341805 | lr 4.0000e-06 | norm: 13.9363 | dt: 1351.72ms | tok/sec: 1551463.66...
step     8 | loss: 10.252383 | lr 4.5000e-06 | norm: 12.7514 | dt: 1309.35ms | tok/sec: 1601670.01...
step     9 | loss: 10.191881 | lr 5.0000e-06 | norm: 11.2892 | dt: 1622.98ms | tok/sec: 1292164.28...
step    10 | loss: 10.133505 | lr 5.5000e-06 | norm: 10.0686 | dt: 1635.85ms | tok/sec: 1281998.13...
step    11 | loss: 10.057395 | lr 6.0000e-06 | norm: 9.0575 | dt: 1303.43ms | tok/sec: 1608944.48...
step    12 | loss: 9.968512 | lr 6.5000e-06 | norm: 8.2433 | dt: 1322.17ms | tok/sec: 1586149.12...
step    13 | loss: 9.917343 | lr 7.0000e-06 | norm: 7.0871 | dt: 1299.27ms | tok/sec: 1614096.78...
step    14 | loss: 9.872045 | lr 7.5000e-06 | norm: 6.3876 | dt: 1356.71ms | tok/sec: 1545762.96...
step    15 | loss: 9.841322 | lr 8.0000e-06 | norm: 5.5140 | dt: 1350.85ms | tok/sec: 1552467.50...
step    16 | loss: 9.771646 | lr 8.5000e-06 | norm: 4.9927 | dt: 1512.76ms | tok/sec: 1386304.61...
step    17 | loss: 9.765921 | lr 9.0000e-06 | norm: 4.3238 | dt: 1448.63ms | tok/sec: 1447682.53...
step    18 | loss: 9.706056 | lr 9.5000e-06 | norm: 4.1141 | dt: 1295.11ms | tok/sec: 1619287.85...
step    19 | loss: 9.721828 | lr 1.0000e-05 | norm: 3.9724 | dt: 1337.40ms | tok/sec: 1568085.03...
ERROR RUNNING GUARDS torch_dynamo_resume_in_forward_at_111 /home/<USER>/notebooks/Ali_embedding/Ali_embedding/models/masked_language_model.py:111
lambda L, G, **___kwargs_ignored:
  utils_device.CURRENT_DEVICE == None and
  ___check_global_state() and
  ___check_torch_function_mode_stack() and
  check_tensor(L['mask_positions'], Tensor, DispatchKeySet(CUDA, BackendSelect, ADInplaceOrView, AutogradCUDA, AutocastCUDA), torch.bool, device=0, requires_grad=False, size=[32, 1024], stride=[1024, 1]) and
  hasattr(L['mask_positions'], '_dynamo_dynamic_indices') == False and
  ___check_obj_id(G['torch'], 140094032642592) and
  ___check_obj_id(G['torch'].where, 140094027494352)

TREE_GUARD_MANAGER:
+- RootGuardManager
| +- DEFAULT_DEVICE: utils_device.CURRENT_DEVICE == None                           # _dynamo/output_graph.py:520 in init_ambient_guards
| +- GLOBAL_STATE: ___check_global_state()
| +- TORCH_FUNCTION_MODE_STACK: ___check_torch_function_mode_stack()
| +- GuardManager: source=L['mask_positions'], accessed_by=FrameLocalsGuardAccessor(key='mask_positions', framelocals_idx=9)
| | +- TENSOR_MATCH: check_tensor(L['mask_positions'], Tensor, DispatchKeySet(CUDA, BackendSelect, ADInplaceOrView, AutogradCUDA, AutocastCUDA), torch.bool, device=0, requires_grad=False, size=[32, 1024], stride=[1024, 1])
| | +- NO_HASATTR: hasattr(L['mask_positions'], '_dynamo_dynamic_indices') == False
| +- GuardManager: source=G, accessed_by=GlobalsGuardAccessor
| | +- GuardManager: source=G['torch'], accessed_by=DictGetItemGuardAccessor('torch')
| | | +- ID_MATCH: ___check_obj_id(G['torch'], 140094032642592)
| | | +- GuardManager: source=G['torch'].where, accessed_by=GetAttrGuardAccessor(where)
| | | | +- ID_MATCH: ___check_obj_id(G['torch'].where, 140094027494352)

Malformed guard:
check_tensor(L['mask_positions'], Tensor, DispatchKeySet(CUDA, BackendSelect, ADInplaceOrView, AutogradCUDA, AutocastCUDA), torch.bool, device=0, requires_grad=False, size=[32, 1024], stride=[1024, 1])
Traceback (most recent call last):
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/train.py", line 348, in <module>
    main()
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/train.py", line 278, in main
    _, loss = model(x, attention_mask=attention_mask, labels=labels)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1637, in forward
    else self._run_ddp_forward(*inputs, **kwargs)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1464, in _run_ddp_forward
    return self.module(*inputs, **kwargs)  # type: ignore[index]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 655, in _fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/models/masked_language_model.py", line 111, in forward
    if mask_positions.any():
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/utils/_config_module.py", line 369, in __getattr__
    def __getattr__(self, name: str) -> Any:

KeyboardInterrupt
[rank0]: Traceback (most recent call last):
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/train.py", line 348, in <module>
[rank0]:     main()
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/train.py", line 278, in main
[rank0]:     _, loss = model(x, attention_mask=attention_mask, labels=labels)
[rank0]:               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1637, in forward
[rank0]:     else self._run_ddp_forward(*inputs, **kwargs)
[rank0]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1464, in _run_ddp_forward
[rank0]:     return self.module(*inputs, **kwargs)  # type: ignore[index]
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 655, in _fn
[rank0]:     return fn(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding/models/masked_language_model.py", line 111, in forward
[rank0]:     if mask_positions.any():
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/utils/_config_module.py", line 369, in __getattr__
[rank0]:     def __getattr__(self, name: str) -> Any:

[rank0]: KeyboardInterrupt
