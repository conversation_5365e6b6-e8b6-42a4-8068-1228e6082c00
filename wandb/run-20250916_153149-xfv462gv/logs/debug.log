2025-09-16 15:31:49,474 INFO    MainThread:228614 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-09-16 15:31:49,475 INFO    MainThread:228614 [wandb_setup.py:_flush():80] Configure stats pid to 228614
2025-09-16 15:31:49,475 INFO    MainThread:228614 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-09-16 15:31:49,475 INFO    MainThread:228614 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/notebooks/Ali_embedding/Ali_embedding/wandb/settings
2025-09-16 15:31:49,475 INFO    MainThread:228614 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-09-16 15:31:49,475 INFO    MainThread:228614 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/notebooks/Ali_embedding/Ali_embedding/wandb/run-20250916_153149-xfv462gv/logs/debug.log
2025-09-16 15:31:49,475 INFO    MainThread:228614 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/notebooks/Ali_embedding/Ali_embedding/wandb/run-20250916_153149-xfv462gv/logs/debug-internal.log
2025-09-16 15:31:49,475 INFO    MainThread:228614 [wandb_init.py:init():830] calling init triggers
2025-09-16 15:31:49,475 INFO    MainThread:228614 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'model': {'n_layer': 12, 'n_head': 12, 'n_embd': 768, 'block_size': 1024, 'vocab_size': 50368, 'mask_prob': 0.15}, 'training': {'total_batch_size': 1048576, 'micro_batch_size': 32, 'sequence_length': 1024, 'max_steps': 20000, 'warmup_steps': 400, 'max_lr': 0.0002, 'min_lr': 2e-05, 'weight_decay': 0.1, 'optimizer_lr': 0.0006, 'embedding_lr_scale': 0.1}, 'data': {'dataset_name': 'finweb 10B', 'target_tokens': 10000000000, 'tokenizer_path': '/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls'}, '_wandb': {}}
2025-09-16 15:31:49,475 INFO    MainThread:228614 [wandb_init.py:init():871] starting backend
2025-09-16 15:31:49,680 INFO    MainThread:228614 [wandb_init.py:init():874] sending inform_init request
2025-09-16 15:31:49,682 INFO    MainThread:228614 [wandb_init.py:init():882] backend started and connected
2025-09-16 15:31:49,683 INFO    MainThread:228614 [wandb_init.py:init():953] updated telemetry
2025-09-16 15:31:49,684 INFO    MainThread:228614 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-09-16 15:31:50,638 INFO    MainThread:228614 [wandb_init.py:init():1029] starting run threads in backend
2025-09-16 15:31:50,727 INFO    MainThread:228614 [wandb_run.py:_console_start():2458] atexit reg
2025-09-16 15:31:50,727 INFO    MainThread:228614 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-09-16 15:31:50,728 INFO    MainThread:228614 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-09-16 15:31:50,728 INFO    MainThread:228614 [wandb_run.py:_redirect():2398] Redirects installed.
2025-09-16 15:31:50,729 INFO    MainThread:228614 [wandb_init.py:init():1075] run started, returning control to user process
2025-09-16 19:44:15,329 INFO    MainThread:228614 [wandb_run.py:_finish():2224] finishing run axiomlaborg-axiom-lab/encoder-mlm-ali/xfv462gv
2025-09-16 19:44:15,330 INFO    MainThread:228614 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-09-16 19:44:15,330 INFO    MainThread:228614 [wandb_run.py:_restore():2405] restore
2025-09-16 19:44:15,330 INFO    MainThread:228614 [wandb_run.py:_restore():2411] restore done
2025-09-16 19:44:20,289 INFO    MainThread:228614 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-09-16 19:44:20,289 INFO    MainThread:228614 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-09-16 19:44:20,289 INFO    MainThread:228614 [wandb_run.py:_footer_sync_info():3864] logging synced files
