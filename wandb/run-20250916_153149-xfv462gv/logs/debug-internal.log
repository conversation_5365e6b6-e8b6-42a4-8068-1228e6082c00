{"time":"2025-09-16T15:31:49.686055852+03:30","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-16T15:31:50.315606574+03:30","level":"INFO","msg":"stream: created new stream","id":"xfv462gv"}
{"time":"2025-09-16T15:31:50.316586011+03:30","level":"INFO","msg":"stream: started","id":"xfv462gv"}
{"time":"2025-09-16T15:31:50.316627774+03:30","level":"INFO","msg":"writer: Do: started","stream_id":"xfv462gv"}
{"time":"2025-09-16T15:31:50.316669697+03:30","level":"INFO","msg":"sender: started","stream_id":"xfv462gv"}
{"time":"2025-09-16T15:31:50.31663739+03:30","level":"INFO","msg":"handler: started","stream_id":"xfv462gv"}
{"time":"2025-09-16T18:22:51.092593987+03:30","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-09-16T18:23:09.552183098+03:30","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/axiomlaborg-axiom-lab/encoder-mlm-ali/xfv462gv/file_stream\": EOF"}
{"time":"2025-09-16T19:44:16.333075211+03:30","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"uploading output.log","runtime_seconds":0.697374388,"progress":"1.0MB/1.9MB"},{"desc":"uploading wandb-summary.json","runtime_seconds":0.697362679,"progress":"320B/320B"},{"desc":"uploading config.yaml","runtime_seconds":0.194322018,"progress":"4.3KB/4.3KB"}],"total_operations":3}}
{"time":"2025-09-16T19:44:19.701176074+03:30","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-09-16T19:44:20.290358594+03:30","level":"INFO","msg":"stream: closing","id":"xfv462gv"}
{"time":"2025-09-16T19:44:20.290379143+03:30","level":"INFO","msg":"handler: closed","stream_id":"xfv462gv"}
{"time":"2025-09-16T19:44:20.290387874+03:30","level":"INFO","msg":"writer: Close: closed","stream_id":"xfv462gv"}
{"time":"2025-09-16T19:44:20.290419454+03:30","level":"INFO","msg":"sender: closed","stream_id":"xfv462gv"}
{"time":"2025-09-16T19:44:20.290470868+03:30","level":"INFO","msg":"stream: closed","id":"xfv462gv"}
