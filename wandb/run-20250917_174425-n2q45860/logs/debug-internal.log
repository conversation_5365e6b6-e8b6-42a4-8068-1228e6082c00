{"time":"2025-09-17T17:44:25.434605+03:30","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-17T17:44:26.217985845+03:30","level":"INFO","msg":"stream: created new stream","id":"n2q45860"}
{"time":"2025-09-17T17:44:26.218022797+03:30","level":"INFO","msg":"stream: started","id":"n2q45860"}
{"time":"2025-09-17T17:44:26.218094583+03:30","level":"INFO","msg":"writer: Do: started","stream_id":"n2q45860"}
{"time":"2025-09-17T17:44:26.218250881+03:30","level":"INFO","msg":"handler: started","stream_id":"n2q45860"}
{"time":"2025-09-17T17:44:26.218372521+03:30","level":"INFO","msg":"sender: started","stream_id":"n2q45860"}
{"time":"2025-09-17T18:20:41.947366315+03:30","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-09-17T18:21:14.321560596+03:30","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-09-17T18:21:48.74102989+03:30","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"context deadline exceeded\",\"path\":[\"project\",\"run\"]}],\"data\":{\"project\":{\"run\":null}}}"}
{"time":"2025-09-17T18:53:12.238707855+03:30","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-09-17T18:53:44.463006311+03:30","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-09-17T18:53:50.901997687+03:30","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/axiomlaborg-axiom-lab/encoder-mlm-ali/n2q45860/file_stream\": context deadline exceeded"}
{"time":"2025-09-17T18:54:18.690710898+03:30","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-09-17T18:54:58.463467968+03:30","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-09-17T23:33:12.986880364+03:30","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-09-17T23:49:28.02648296+03:30","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-09-17T23:49:31.135335007+03:30","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/files/axiomlaborg-axiom-lab/encoder-mlm-ali/n2q45860/file_stream","body":"{\"error\":\"An internal error occurred. Please contact support.\"}"}
{"time":"2025-09-17T23:49:36.710701964+03:30","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"An internal error occurred. Please contact support.\",\"path\":[\"project\"]}],\"data\":{\"project\":null}}"}
{"time":"2025-09-17T23:49:54.715667056+03:30","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"An internal error occurred. Please contact support.\",\"path\":[\"project\"]}],\"data\":{\"project\":null}}"}
{"time":"2025-09-17T23:50:03.377911492+03:30","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/files/axiomlaborg-axiom-lab/encoder-mlm-ali/n2q45860/file_stream","body":"{\"error\":\"An internal error occurred. Please contact support.\"}"}
{"time":"2025-09-17T23:50:03.456141283+03:30","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"An internal error occurred. Please contact support.\",\"path\":[\"project\"]}],\"data\":{\"project\":null}}"}
{"time":"2025-09-18T00:12:08.021943405+03:30","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"uploading output.log","runtime_seconds":0.704306611},{"desc":"uploading wandb-summary.json","runtime_seconds":0.704275919}],"total_operations":2}}
{"time":"2025-09-18T00:12:10.7100196+03:30","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-09-18T00:12:11.333485652+03:30","level":"INFO","msg":"stream: closing","id":"n2q45860"}
{"time":"2025-09-18T00:12:11.333501101+03:30","level":"INFO","msg":"handler: closed","stream_id":"n2q45860"}
{"time":"2025-09-18T00:12:11.333518338+03:30","level":"INFO","msg":"sender: closed","stream_id":"n2q45860"}
{"time":"2025-09-18T00:12:11.333509687+03:30","level":"INFO","msg":"writer: Close: closed","stream_id":"n2q45860"}
{"time":"2025-09-18T00:12:11.333580775+03:30","level":"INFO","msg":"stream: closed","id":"n2q45860"}
