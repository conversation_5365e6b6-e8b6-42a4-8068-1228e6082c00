_wandb:
    value:
        cli_version: 0.21.0
        e:
            wc714g8xvfwryoo35roz0ddq387mnyfe:
                codePath: train.py
                codePathLocal: train.py
                cpu_count: 112
                cpu_count_logical: 112
                cudaVersion: "12.8"
                disk:
                    /:
                        total: "1511024451584"
                        used: "1448842215424"
                email: <EMAIL>
                executable: /opt/anaconda3/envs/llm_env/bin/python3.12
                gpu: NVIDIA H100 80GB HBM3
                gpu_count: 8
                gpu_nvidia:
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-77194a63-0847-1f11-ca76-93b042e00e4e
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-a4339cc5-9120-434c-0efd-72e07743bd78
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-23269b3b-5279-4644-f19a-9271181482ea
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-ba724632-f670-4b14-dafd-acce0f0e9962
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-488886e3-ab3f-2a65-b7cf-3e53717dd5d0
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-83acd5d5-35cc-9096-017a-4ca54c2b2e4e
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-2db63487-870f-eb77-f005-971e9a97ef57
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-347d855e-f066-042d-3ee4-3a8d5584a599
                host: h82
                memory:
                    total: "1651351687168"
                os: Linux-5.15.0-153-generic-x86_64-with-glibc2.35
                program: /home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/train.py
                python: CPython 3.12.9
                root: /home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls
                startedAt: "2025-09-17T14:14:25.221879Z"
                writerId: wc714g8xvfwryoo35roz0ddq387mnyfe
        m: []
        python_version: 3.12.9
        t:
            "1":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 71
                - 105
            "2":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 71
                - 105
            "3":
                - 2
                - 13
                - 15
                - 16
                - 61
            "4": 3.12.9
            "5": 0.21.0
            "6": 4.55.0
            "12": 0.21.0
            "13": linux-x86_64
data:
    value:
        dataset_name: finweb 10B
        target_tokens: 10000000000
        tokenizer_path: /s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls
model:
    value:
        block_size: 1024
        mask_prob: 0.15
        n_embd: 768
        n_head: 12
        n_layer: 12
        vocab_size: 50368
training:
    value:
        embedding_lr_scale: 0.1
        max_lr: 0.0002
        max_steps: 20000
        micro_batch_size: 32
        min_lr: 2e-05
        optimizer_lr: 0.0006
        sequence_length: 1024
        total_batch_size: 1048576
        warmup_steps: 400
        weight_decay: 0.1
