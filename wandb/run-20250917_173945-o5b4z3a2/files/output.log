Wandb initialized: https://wandb.ai/axiomlaborg-axiom-lab/encoder-mlm-ali/runs/o5b4z3a2
Tokenizer vocab size: 50368
total desired batch size: 1048576
micro batch size: 32
=> calculated gradient accumulation steps: 4
Found 97 .arrow files in sample-10BT/train
Total: 97 .arrow files for split train
Using streaming approach - no pre-processing
Loading file: /s2_nfs/fineweb-edu/localds/sample-10BT/train/data-00023-of-00097.arrow
Found 16 .arrow files in sample-10BT/test
Total: 16 .arrow files for split test
Using streaming approach - no pre-processing
Loading file: /s2_nfs/fineweb-edu/localds/sample-10BT/test/data-00000-of-00016.arrow
Warning: tokenizer.cls_token_ids is not a list: 1, creating fallback
Regular params (lr=6.00e-04): decay=126,410,496, no_decay=38,400
Embedding params (lr=6.00e-05): decay=38,670,336, no_decay=0
Multi-CLS embedding params (lr=6.00e-05): 12,288
using fused AdamW: True
### starting training loop of Encoder-<PERSON><PERSON> on finweb 10B @2025-09-17 17:39:56.502478
Total trainable parameters: 165_131_520
torch.compile: True
=> Batch_size:32; Sequence_length:1024; max_lr:0.0002; min_lr:2e-05
=> Target tokens: 10B; Total steps: 20000; Warmup steps: 400
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
[rank0]:W0917 17:39:59.056000 394671 site-packages/torch/_dynamo/variables/tensor.py:913] [7/0] Graph break from `Tensor.item()`, consider setting:
[rank0]:W0917 17:39:59.056000 394671 site-packages/torch/_dynamo/variables/tensor.py:913] [7/0]     torch._dynamo.config.capture_scalar_outputs = True
[rank0]:W0917 17:39:59.056000 394671 site-packages/torch/_dynamo/variables/tensor.py:913] [7/0] or:
[rank0]:W0917 17:39:59.056000 394671 site-packages/torch/_dynamo/variables/tensor.py:913] [7/0]     env TORCHDYNAMO_CAPTURE_SCALAR_OUTPUTS=1
[rank0]:W0917 17:39:59.056000 394671 site-packages/torch/_dynamo/variables/tensor.py:913] [7/0] to include these operations in the captured graph.
[rank0]:W0917 17:39:59.056000 394671 site-packages/torch/_dynamo/variables/tensor.py:913] [7/0]
[rank0]:W0917 17:39:59.056000 394671 site-packages/torch/_dynamo/variables/tensor.py:913] [7/0] Graph break: from user code at:
[rank0]:W0917 17:39:59.056000 394671 site-packages/torch/_dynamo/variables/tensor.py:913] [7/0]   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/models/encoder_block.py", line 32, in forward
[rank0]:W0917 17:39:59.056000 394671 site-packages/torch/_dynamo/variables/tensor.py:913] [7/0]     x = x + self.attn(self.ln_1(x), attention_mask=attention_mask)
[rank0]:W0917 17:39:59.056000 394671 site-packages/torch/_dynamo/variables/tensor.py:913] [7/0]   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/models/attention.py", line 130, in forward
[rank0]:W0917 17:39:59.056000 394671 site-packages/torch/_dynamo/variables/tensor.py:913] [7/0]     max_effective_len = seq_lengths.max().item()
[rank0]:W0917 17:39:59.056000 394671 site-packages/torch/_dynamo/variables/tensor.py:913] [7/0]
[rank0]:W0917 17:39:59.056000 394671 site-packages/torch/_dynamo/variables/tensor.py:913] [7/0]
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Step 0: validation loss (MLM): 10.9290
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
step     0 | loss: 10.927363 | lr 5.0000e-07 | norm: 24.7778 | dt: 18521.79ms | tok/sec: 56613.11...
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Warning: Found token IDs >= vocab_size (50352): tensor([50367], device='cuda:0')
Traceback (most recent call last):
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/train.py", line 293, in main
    loss.backward()
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/autograd/__init__.py", line 353, in backward
    _engine_run_backward(
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/autograd/graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: CUDA error: unrecognized error code
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/train.py", line 348, in <module>
    main()
  File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/train.py", line 295, in main
    print(f"Backward pass failed at step {step}, micro_step {micro_step}: {e}")
                                         ^^^^^^
KeyboardInterrupt
[rank0]: Traceback (most recent call last):
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/train.py", line 293, in main
[rank0]:     loss.backward()
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_tensor.py", line 648, in backward
[rank0]:     torch.autograd.backward(
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/autograd/__init__.py", line 353, in backward
[rank0]:     _engine_run_backward(
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/autograd/graph.py", line 824, in _engine_run_backward
[rank0]:     return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]: RuntimeError: CUDA error: unrecognized error code
[rank0]: CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
[rank0]: For debugging consider passing CUDA_LAUNCH_BLOCKING=1
[rank0]: Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.


[rank0]: During handling of the above exception, another exception occurred:

[rank0]: Traceback (most recent call last):
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/train.py", line 348, in <module>
[rank0]:     main()
[rank0]:   File "/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/train.py", line 295, in main
[rank0]:     print(f"Backward pass failed at step {step}, micro_step {micro_step}: {e}")
[rank0]:                                          ^^^^^^
[rank0]: KeyboardInterrupt
