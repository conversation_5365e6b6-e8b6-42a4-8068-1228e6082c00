#!/usr/bin/env python3
"""
Simple debug script to test data loading functionality and verify the attention mask fix.
"""

import torch
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.dataloader import DataLoaderLite

def debug_batch_content(inputs, labels, attention_mask, batch_idx=0):
    """Debug and print detailed information about a batch"""
    print(f"\n=== BATCH {batch_idx} DEBUG ===")
    
    # Basic tensor info
    print(f"Inputs shape: {inputs.shape}")
    print(f"Labels shape: {labels.shape}")
    print(f"Attention mask shape: {attention_mask.shape}")
    
    # Check for problematic values
    print(f"\nInputs stats:")
    print(f"  Min: {inputs.min().item()}, Max: {inputs.max().item()}")
    print(f"  Unique values: {torch.unique(inputs).numel()}")
    print(f"  Unique input tokens: {torch.unique(inputs).tolist()}")
    
    print(f"\nLabels stats:")
    print(f"  Min: {labels.min().item()}, Max: {labels.max().item()}")
    print(f"  Non-ignore positions: {(labels != -100).sum().item()}")
    print(f"  Unique labels: {torch.unique(labels[labels != -100]).numel()}")
    
    print(f"\nAttention mask stats:")
    print(f"  Sum: {attention_mask.sum().item()}")
    print(f"  Non-zero positions: {(attention_mask != 0).sum().item()}")
    
    # Sample sequences (first sequence in batch)
    seq_idx = 0
    print(f"\n--- Sample Sequence {seq_idx} ---")
    print(f"inputs[{seq_idx},:20]: {inputs[seq_idx,:20].tolist()}")
    print(f"labels[{seq_idx},:20]: {labels[seq_idx,:20].tolist()}")
    print(f"attention_mask[{seq_idx},:20]: {attention_mask[seq_idx,:20].tolist()}")
    
    # Check for masked positions
    masked_positions = (labels[seq_idx] != -100)
    if masked_positions.any():
        masked_indices = torch.nonzero(masked_positions, as_tuple=False).squeeze(-1)
        print(f"Masked positions in seq {seq_idx}: {masked_indices.tolist()}")
        print(f"Original tokens at masked positions: {labels[seq_idx][masked_indices].tolist()}")
        print(f"Input tokens at masked positions: {inputs[seq_idx][masked_indices].tolist()}")
    else:
        print(f"WARNING: No masked positions found in sequence {seq_idx}!")
    
    # Check attention mask validity
    if attention_mask[seq_idx].sum() == 0:
        print(f"ERROR: All-zero attention mask for sequence {seq_idx}!")
    else:
        print(f"Attention mask OK: {attention_mask[seq_idx].sum().item()} non-zero positions")

def debug_token_generation(data_loader):
    """Debug the token generation pipeline to see where it's failing"""
    print(f"\n=== TOKEN GENERATION DEBUG ===")
    
    # Check if data files exist
    print(f"Data files: {data_loader.data_files}")
    for i, file_path in enumerate(data_loader.data_files):
        exists = file_path.exists() if hasattr(file_path, 'exists') else False
        print(f"  File {i}: {file_path} - Exists: {exists}")
    
    # Check current file loading
    print(f"Current file index: {data_loader.current_file_idx}")
    print(f"Current shard data: {data_loader.current_shard_data is not None}")
    
    # Check if raw texts are loaded
    if hasattr(data_loader, 'raw_texts'):
        print(f"Raw texts loaded: {len(data_loader.raw_texts)} chunks")
        if data_loader.raw_texts:
            print(f"Sample text: '{data_loader.raw_texts[0][:100]}...'")
    else:
        print("No raw_texts attribute found")
    
    # Test token generation directly
    print(f"\n--- Testing get_tokens_for_sequence ---")
    try:
        tokens = data_loader.get_tokens_for_sequence()
        if tokens:
            print(f"Generated tokens: {len(tokens)} tokens")
            print(f"Sample tokens: {tokens[:10]}")
            
            # Decode to see what text these represent
            decoded = data_loader.tokenizer.decode(tokens[:20])
            print(f"Decoded sample: '{decoded}'")
        else:
            print("No tokens generated - get_tokens_for_sequence returned None")
    except Exception as e:
        print(f"Error in get_tokens_for_sequence: {e}")
        import traceback
        traceback.print_exc()
    
    # Check tokenizer special tokens
    print(f"\n--- Tokenizer Special Tokens ---")
    print(f"PAD token: '{data_loader.tokenizer.pad_token}' (ID: {data_loader.pad_token_id})")
    print(f"CLS token: '{data_loader.tokenizer.cls_token}' (ID: {data_loader.cls_token_id})")
    print(f"EOS token: '{data_loader.tokenizer.eos_token}' (ID: {data_loader.eos_token_id})")
    print(f"MASK token: '{data_loader.tokenizer.mask_token}' (ID: {data_loader.mask_token_id})")

def test_dataloader():
    """Test the dataloader and check for common issues"""
    print("=== DATALOADER DEBUG TEST ===")
    
    # Initialize dataloader with small batch for testing
    B, T = 4, 64  # Small batch size and sequence length for debugging
    
    try:
        # Create dataloader
        print(f"Creating DataLoaderLite with B={B}, T={T}")
        data_loader = DataLoaderLite(
            B=B, 
            T=T, 
            process_rank=0, 
            num_processes=1, 
            split="train"
        )
        print("✓ DataLoader created successfully")
        
        # Test tokenizer integration
        tokenizer = data_loader.tokenizer
        print(f"✓ Using tokenizer: {type(tokenizer).__name__}")
        print(f"  CLS token ID: {tokenizer.cls_token_id}")
        print(f"  EOS token ID: {tokenizer.eos_token_id}")
        print(f"  PAD token ID: {tokenizer.pad_token_id}")
        print(f"  MASK token ID: {tokenizer.mask_token_id}")
        print(f"  Vocab size: {tokenizer.vocab_size}")
        
        # Debug token generation pipeline
        debug_token_generation(data_loader)
        
        # Test multiple batches
        for batch_idx in range(3):
            print(f"\n{'='*50}")
            print(f"Testing batch {batch_idx + 1}/3")
            
            try:
                inputs, labels, attention_mask = data_loader.next_batch()
                print("✓ Batch generated successfully")
                
                # Debug batch content
                debug_batch_content(inputs, labels, attention_mask, batch_idx)
                
                # Check for critical issues
                issues = []
                
                # Check 1: All-zero attention masks
                if attention_mask.sum() == 0:
                    issues.append("All-zero attention masks")
                
                # Check 2: All -100 labels
                if (labels == -100).all():
                    issues.append("All labels are -100 (no masked tokens)")
                
                # Check 3: NaN or Inf values
                if torch.isnan(inputs).any() or torch.isinf(inputs).any():
                    issues.append("NaN/Inf in inputs")
                
                if torch.isnan(labels.float()).any() or torch.isinf(labels.float()).any():
                    issues.append("NaN/Inf in labels")
                
                # Check 4: Invalid token IDs
                if inputs.min() < 0:
                    issues.append("Negative token IDs in inputs")
                
                # Check 5: All PAD tokens
                if (inputs == data_loader.pad_token_id).all():
                    issues.append("All tokens are PAD tokens - no content loaded")
                
                # Report issues
                if issues:
                    print(f"\n❌ ISSUES FOUND:")
                    for issue in issues:
                        print(f"  - {issue}")
                else:
                    print(f"\n✅ Batch {batch_idx + 1} looks healthy!")
                    
            except Exception as e:
                print(f"❌ Error generating batch {batch_idx + 1}: {e}")
                import traceback
                traceback.print_exc()
                break
        
        print(f"\n{'='*50}")
        print("DATALOADER TEST COMPLETE")
        
    except Exception as e:
        print(f"❌ Error creating dataloader: {e}")
        import traceback
        traceback.print_exc()

def test_tokenizer_integration():
    """Test tokenizer integration using the dataloader's tokenizer"""
    print("\n=== TOKENIZER INTEGRATION TEST ===")
    
    try:
        # Create a minimal dataloader just to get the tokenizer
        data_loader = DataLoaderLite(B=1, T=32, process_rank=0, num_processes=1, split="train")
        tokenizer = data_loader.tokenizer
        
        print("✓ Tokenizer loaded successfully from dataloader")
        
        # Test special tokens
        print(f"CLS token ID: {tokenizer.cls_token_id}")
        print(f"EOS token ID: {tokenizer.eos_token_id}")
        print(f"PAD token ID: {tokenizer.pad_token_id}")
        print(f"MASK token ID: {tokenizer.mask_token_id}")
        print(f"Vocab size: {tokenizer.vocab_size}")
        
        # Test encoding/decoding
        test_text = "Hello world, this is a test."
        tokens = tokenizer.encode(test_text, add_special_tokens=False)
        decoded = tokenizer.decode(tokens)
        
        print(f"\nTest text: '{test_text}'")
        print(f"Encoded tokens: {tokens[:10]}...")  # Show first 10 tokens
        print(f"Decoded text: '{decoded}'")
        
        if decoded.strip() == test_text.strip():
            print("✅ Tokenizer encode/decode works correctly")
        else:
            print("❌ Tokenizer encode/decode mismatch")
            
    except Exception as e:
        print(f"❌ Error testing tokenizer: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Starting dataloader debug session...")
    
    # Test tokenizer first
    test_tokenizer_integration()
    
    # Test dataloader
    test_dataloader()
    
    print("\nDebug session complete!")
