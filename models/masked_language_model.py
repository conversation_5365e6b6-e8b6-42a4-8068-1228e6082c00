import torch
import torch.nn as nn
from torch.nn import functional as F
import inspect

from .encoder_block import EncoderBlock
from .config import load_config_from_yaml


class MaskedLanguageModel(nn.Module):
    """Bidirectional Transformer Encoder for Masked Language Modeling with weight tying"""

    def __init__(self, config, tokenizer=None):
        super().__init__()
        self.config = config
        
        # Load attention config once for all layers (optimization)
        # But override with model config parameters to ensure consistency
        try:
            self.attention_config = load_config_from_yaml("config.yaml")
            # Override with model config to ensure consistency
            self.attention_config.n_embd = config.n_embd
            self.attention_config.n_head = config.n_head
            self.attention_config.n_head_4 = config.n_head_4
            self.attention_config.n_layer = config.n_layer
            self.attention_config.block_size = config.block_size
        except:
            # Fallback: create attention config from model config
            from .config import AttentionConfig
            self.attention_config = AttentionConfig(
                n_embd=config.n_embd,
                n_head=config.n_head,
                n_head_4=config.n_head_4,
                n_layer=config.n_layer,
                block_size=config.block_size,
                layer_id=0
            )
        
        # Multi-CLS configuration
        self.use_multi_cls = config.use_multi_cls
        self.n_cls_tokens = config.n_cls_tokens if self.use_multi_cls else 1
        
        # If tokenizer is provided, use its vocabulary and special tokens
        if tokenizer is not None:
            self.tokenizer = tokenizer
            effective_vocab_size = len(tokenizer)
            self.mask_token_id = tokenizer.mask_token_id
            self.pad_token_id = tokenizer.pad_token_id
            self.cls_token_id = tokenizer.cls_token_id
            self.eos_token_id = tokenizer.eos_token_id
            
            # Add multi-CLS configuration to tokenizer for data loader coordination
            tokenizer.use_multi_cls = self.use_multi_cls
            tokenizer.n_cls_tokens = self.n_cls_tokens
            
            # For multi-CLS, we need additional CLS token IDs
            if self.use_multi_cls and hasattr(tokenizer, 'cls_token_ids'):
                # Check if tokenizer.cls_token_ids is actually a list
                if isinstance(tokenizer.cls_token_ids, list):
                    self.cls_token_ids = tokenizer.cls_token_ids
                else:
                    print(f"Warning: tokenizer.cls_token_ids is not a list: {tokenizer.cls_token_ids}, creating fallback")
                    vocab_size = len(tokenizer)
                    cls_start_id = max(vocab_size + 100, 60000)  # Use a safe high range
                    self.cls_token_ids = list(range(cls_start_id, cls_start_id + self.n_cls_tokens))
            elif self.use_multi_cls:
                # For multi-CLS, we use special IDs that won't conflict with regular tokens
                # These IDs are handled separately in the forward pass
                vocab_size = len(tokenizer)
                # Ensure CLS token IDs are well outside the tokenizer vocabulary
                cls_start_id = max(vocab_size + 100, 60000)  # Use a safe high range
                self.cls_token_ids = list(range(cls_start_id, cls_start_id + self.n_cls_tokens))
                # Also add to tokenizer for data loader coordination
                tokenizer.cls_token_ids = self.cls_token_ids
                print(f"Model: Created CLS token IDs: {self.cls_token_ids} (tokenizer vocab_size: {vocab_size})")
            else:
                self.cls_token_ids = []
        else:
            # Fallback to original behavior
            self.mask_token_id = config.vocab_size      # id for [MASK]
            self.pad_token_id = config.vocab_size + 1   # id for [PAD]
            self.cls_token_id = config.vocab_size + 2   # id for [CLS]
            self.eos_token_id = config.vocab_size + 3   # id for [EOS]
            if self.use_multi_cls:
                self.cls_token_ids = list(range(config.vocab_size + 2, config.vocab_size + 2 + self.n_cls_tokens))
                effective_vocab_size = config.vocab_size + 4 + self.n_cls_tokens - 1
            else:
                self.cls_token_ids = []
                effective_vocab_size = config.vocab_size + 4

        # Model architecture based on CLS strategy
        if self.use_multi_cls:
            # Multi-CLS approach: separate embeddings for CLS tokens
            # Regular embedding layer needs to handle all tokenizer tokens
            # CLS tokens (with IDs 60000+) will be handled by separate parameters
            if tokenizer is not None:
                regular_vocab_size = len(tokenizer)  # Use actual tokenizer size
            else:
                regular_vocab_size = effective_vocab_size
                
            self.transformer = nn.ModuleDict(dict(
                wte = nn.Embedding(regular_vocab_size, config.n_embd),  # Tokenizer vocab size
                h = nn.ModuleList([EncoderBlock(config, self.attention_config, layer_id=i) for i in range(config.n_layer)]),
                ln_f = nn.LayerNorm(config.n_embd),
            ))
            # Separate CLS token embeddings as nn.Parameter
            self.cls_embeddings = nn.Parameter(torch.randn(self.n_cls_tokens, config.n_embd) * 0.02)
            # Fast CLS weighting projection
            self.cls_weight_proj = nn.Linear(config.n_embd, 1, bias=False)
        else:
            # Standard CLS+EOS approach
            self.transformer = nn.ModuleDict(dict(
                wte = nn.Embedding(effective_vocab_size, config.n_embd),
                h = nn.ModuleList([EncoderBlock(config, self.attention_config, layer_id=i) for i in range(config.n_layer)]),
                ln_f = nn.LayerNorm(config.n_embd),
            ))
            # Gate projection for hidden state-based interpolation
            self.hidden_gate = nn.Linear(config.n_embd, config.n_embd, bias=False)
        
        # MLM head (common for both approaches)
        if self.use_multi_cls and tokenizer is not None:
            # For multi-CLS, MLM head only needs to predict regular tokens
            mlm_vocab_size = len(tokenizer)
        else:
            mlm_vocab_size = effective_vocab_size
        self.lm_head = nn.Linear(config.n_embd, mlm_vocab_size, bias=False)

        # Enable gradient checkpointing for memory efficiency
        self.gradient_checkpointing = False

        # init params
        self.apply(self._init_weights)

    def enable_gradient_checkpointing(self):
        """Enable gradient checkpointing to save memory"""
        self.gradient_checkpointing = True
        
    def disable_gradient_checkpointing(self):
        """Disable gradient checkpointing"""
        self.gradient_checkpointing = False
    
    def get_cls_config(self):
        """Get CLS configuration for data loader"""
        return {
            'use_multi_cls': self.use_multi_cls,
            'n_cls_tokens': self.n_cls_tokens,
            'cls_token_ids': self.cls_token_ids if self.use_multi_cls else None
        }

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            std = 0.02
            if hasattr(module, 'NANOGPT_SCALE_INIT'):
                std *= (2 * self.config.n_layer) ** -0.5
            torch.nn.init.normal_(module.weight, mean=0.0, std=std)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)

    def compute_cls_weighted_representation(self, x, final_block, attention_mask=None):
        """Fast similarity-based weighted average of CLS tokens for MLM (multi-CLS mode only)."""
        if not self.use_multi_cls:
            return None
            
        B, T, C = x.size()
        n_cls_tokens = self.n_cls_tokens
        
        # Apply layer norm first
        x_normed = final_block.ln_1(x)
        
        # Extract CLS tokens only (much faster than processing all tokens)
        cls_tokens = x_normed[:, :n_cls_tokens, :]  # (B, n_cls_tokens, C)
        
        # Fast learned weights from CLS tokens directly
        raw_weights = self.cls_weight_proj(cls_tokens).squeeze(-1)  # (B, n_cls_tokens)
        weights = F.softmax(raw_weights, dim=-1)  # (B, n_cls_tokens)
        
        # Efficient weighted sum using einsum
        weighted_cls = torch.einsum('bi,bic->bc', weights, cls_tokens)  # (B, C)
        
        return weighted_cls

    def forward(self, idx, attention_mask=None, labels=None):
        # idx is of shape (B, T)
        B, T = idx.size()
        assert T <= self.config.block_size, f"Cannot forward sequence of length {T}, block size is only {self.config.block_size}"
        
        # Handle embeddings based on strategy
        if self.use_multi_cls:
            # Multi-CLS approach: handle regular tokens and CLS tokens separately
            tok_emb = torch.zeros(B, T, self.config.n_embd, device=idx.device, dtype=self.transformer.wte.weight.dtype)
            
            # Regular tokens (including MASK, PAD, EOS but not CLS tokens)
            regular_mask = torch.ones_like(idx, dtype=torch.bool)
            for cls_id in self.cls_token_ids:
                regular_mask = regular_mask & (idx != cls_id)
            
            if regular_mask.any():
                # Ensure token IDs are within vocabulary range
                regular_tokens = idx[regular_mask]
                vocab_size = self.transformer.wte.num_embeddings
                
                # Clamp token IDs to valid range and warn about out-of-range tokens
                if (regular_tokens >= vocab_size).any():
                    print(f"Warning: Found token IDs >= vocab_size ({vocab_size}): {regular_tokens[regular_tokens >= vocab_size].unique()}")
                    regular_tokens = torch.clamp(regular_tokens, 0, vocab_size - 1)
                
                tok_emb[regular_mask] = self.transformer.wte(regular_tokens)
            
            # CLS tokens use separate embeddings
            for i, cls_id in enumerate(self.cls_token_ids):
                cls_mask = (idx == cls_id)
                if cls_mask.any():
                    tok_emb[cls_mask] = self.cls_embeddings[i]
        else:
            # Standard approach: use single embedding layer
            tok_emb = self.transformer.wte(idx)  # (B, T, n_embd)
        
        x = tok_emb
        
        # Forward through all transformer layers
        for block in self.transformer.h:
            if self.gradient_checkpointing:
                x = torch.utils.checkpoint.checkpoint(block, x, attention_mask)
            else:
                x = block(x, attention_mask=attention_mask)
        # Apply final layer norm
        x = self.transformer.ln_f(x)
        
        # Generate predictions based on strategy
        if self.use_multi_cls:
            # Multi-CLS approach: use weighted CLS representation
            if len(self.transformer.h) > 0:
                weighted_cls = self.compute_cls_weighted_representation(x, self.transformer.h[-1], attention_mask)
            else:
                # Fallback: simple mean of CLS tokens
                cls_tokens = x[:, :self.n_cls_tokens, :]
                weighted_cls = cls_tokens.mean(dim=1)  # (B, C)
            
            # Apply final layer norm to weighted CLS
            weighted_cls = self.transformer.ln_f(weighted_cls)  # (B, C)
            
            # Compute logits from weighted CLS representation
            cls_logits = self.lm_head(weighted_cls)  # (B, vocab_size)
            # Repeat for all positions
            logits = cls_logits.unsqueeze(1).repeat(1, T, 1)  # (B, T, vocab_size)
        else:
            # Standard CLS+EOS approach (existing logic)
            # Extract CLS and EOS token embeddings
            cls = x[:, 0, :]  # (B, d) - CLS token at position 0
            
            # Find EOS token positions (last non-pad token before padding)
            if attention_mask is not None:
                # Find the last non-pad position for each sequence
                seq_lengths = attention_mask.sum(dim=1)  # (B,)
                eos_positions = seq_lengths - 1  # EOS should be at the last valid position
                # Extract EOS tokens using advanced indexing
                batch_indices = torch.arange(B, device=x.device)
                eos = x[batch_indices, eos_positions, :]  # (B, d)
            else:
                # Fallback: assume EOS is at position T-1
                eos = x[:, -1, :]  # (B, d)
            
            # For MLM loss, we only need to compute logits for positions that have labels != -100
            # This means we only interpolate CLS+EOS for masked positions
            if labels is not None:
                # Find positions where labels != -100 (i.e., masked positions)
                mask_positions = (labels != -100)  # (B, T)
                
                if mask_positions.any():
                    # Get indices of masked positions
                    batch_indices, pos_indices = torch.where(mask_positions)
                    
                    # Create interpolation weights only for masked positions
                    if attention_mask is not None:
                        # Use actual EOS positions for normalization
                        eos_pos_for_masked = eos_positions[batch_indices].float()  # (num_masked,)
                        # Normalize positions: alpha = position / eos_position
                        alpha_masked = pos_indices.float() / torch.clamp(eos_pos_for_masked, min=1.0)  # (num_masked,)
                        alpha_masked = torch.clamp(alpha_masked, 0.0, 1.0)  # (num_masked,)
                    else:
                        # Fallback: normalize by T-1
                        alpha_masked = pos_indices.float() / (T - 1) if T > 1 else torch.zeros_like(pos_indices.float())
                    
                    # Get CLS and EOS for the relevant batches
                    cls_for_masked = cls[batch_indices]  # (num_masked, d)
                    eos_for_masked = eos[batch_indices]  # (num_masked, d)
                    
                    # Get hidden states for masked positions
                    hidden_for_masked = x[batch_indices, pos_indices, :]  # (num_masked, d)
                    
                    # Position-wise interpolation only for masked positions
                    alpha_expanded = alpha_masked.unsqueeze(-1)  # (num_masked, 1)
                    pos_interpolated_masked = (1 - alpha_expanded) * cls_for_masked + alpha_expanded * eos_for_masked  # (num_masked, d)
                    
                    # Hidden state-based interpolation using gate
                    gate_weights = torch.sigmoid(self.hidden_gate(hidden_for_masked))  # (num_masked, d)
                    hidden_interpolated_masked = cls_for_masked * gate_weights + eos_for_masked * (1 - gate_weights)  # (num_masked, d)
                    
                    # Combine both interpolations using mean
                    final_interpolated_masked = (pos_interpolated_masked + hidden_interpolated_masked) / 2.0  # (num_masked, d)
                    
                    # Compute logits only for masked positions
                    logits_masked = self.lm_head(final_interpolated_masked)  # (num_masked, vocab_size)
                    
                    # Create full logits tensor (we only need masked positions for loss)
                    logits = torch.zeros(B, T, logits_masked.size(-1), device=x.device, dtype=logits_masked.dtype)
                    logits[batch_indices, pos_indices] = logits_masked
                else:
                    # No masked positions, create empty logits
                    logits = torch.zeros(B, T, self.lm_head.out_features, device=x.device, dtype=x.dtype)
            else:
                # For inference/evaluation, we might need full interpolation (fallback)
                # Position-wise interpolation of CLS and EOS for all positions
                positions = torch.arange(T, device=x.device).float()  # (T,)
                
                if attention_mask is not None:
                    # Use actual EOS positions for normalization
                    eos_positions_expanded = eos_positions.unsqueeze(1).float()  # (B, 1)
                    alpha = positions.unsqueeze(0) / torch.clamp(eos_positions_expanded, min=1.0)  # (B, T)
                    alpha = torch.clamp(alpha, 0.0, 1.0)  # (B, T)
                    alpha = alpha.unsqueeze(-1)  # (B, T, 1)
                else:
                    alpha = positions / (T - 1) if T > 1 else torch.zeros_like(positions)  # (T,)
                    alpha = alpha.unsqueeze(0).unsqueeze(-1)  # (1, T, 1)
                
                # Expand CLS and EOS to all positions
                cls_expanded = cls.unsqueeze(1).expand(-1, T, -1)  # (B, T, d)
                eos_expanded = eos.unsqueeze(1).expand(-1, T, -1)  # (B, T, d)
                
                # Position-wise interpolation: (1-alpha) * CLS + alpha * EOS
                pos_interpolated = (1 - alpha) * cls_expanded + alpha * eos_expanded  # (B, T, d)
                
                # Hidden state-based interpolation using gate
                gate_weights = torch.sigmoid(self.hidden_gate(x))  # (B, T, d)
                hidden_interpolated = cls_expanded * gate_weights + eos_expanded * (1 - gate_weights)  # (B, T, d)
                
                # Combine both interpolations using mean
                final_interpolated = (pos_interpolated + hidden_interpolated) / 2.0  # (B, T, d)
                
                # MLM head: use combined interpolated embeddings for predictions
                logits = self.lm_head(final_interpolated)  # (B, T, vocab_size)
        
        loss = None
        # MLM loss: labels should be -100 for non-masked positions (and PAD positions)
        if labels is not None:
            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), labels.view(-1), ignore_index=-100)
        return logits, loss

    def configure_optimizers(self, weight_decay, learning_rate, device_type, embedding_lr_scale=0.1):
        # start with all of the candidate parameters (that require grad)
        param_dict = {pn: p for pn, p in self.named_parameters()}
        param_dict = {pn: p for pn, p in param_dict.items() if p.requires_grad}
        
        # Separate parameters into different groups based on type and learning rate
        cls_embedding_params = []  # For multi-CLS embeddings (when use_multi_cls=True)
        embedding_decay_params = []  # For regular embeddings
        embedding_nodecay_params = []
        regular_decay_params = []
        regular_nodecay_params = []
        
        for pn, p in param_dict.items():
            # Check parameter types
            is_cls_embedding = self.use_multi_cls and 'cls_embeddings' in pn
            is_regular_embedding = 'transformer.wte' in pn
            
            if is_cls_embedding:
                cls_embedding_params.append(p)
            elif p.dim() >= 2:
                if is_regular_embedding:
                    embedding_decay_params.append(p)
                else:
                    regular_decay_params.append(p)
            else:
                if is_regular_embedding:
                    embedding_nodecay_params.append(p)
                else:
                    regular_nodecay_params.append(p)
        
        # Create optimizer groups with different learning rates
        optim_groups = []
        
        # Regular parameters (full learning rate)
        if regular_decay_params:
            optim_groups.append({
                'params': regular_decay_params, 
                'weight_decay': weight_decay,
                'lr': learning_rate
            })
        if regular_nodecay_params:
            optim_groups.append({
                'params': regular_nodecay_params, 
                'weight_decay': 0.0,
                'lr': learning_rate
            })
        
        # Regular embedding parameters (reduced learning rate)
        embedding_lr = learning_rate * embedding_lr_scale
        if embedding_decay_params:
            optim_groups.append({
                'params': embedding_decay_params, 
                'weight_decay': weight_decay,
                'lr': embedding_lr
            })
        if embedding_nodecay_params:
            optim_groups.append({
                'params': embedding_nodecay_params, 
                'weight_decay': 0.0,
                'lr': embedding_lr
            })
        
        # Multi-CLS embedding parameters (separate learning rate when use_multi_cls=True)
        if self.use_multi_cls and cls_embedding_params:
            cls_embedding_lr = learning_rate * self.config.cls_embedding_lr_scale
            optim_groups.append({
                'params': cls_embedding_params, 
                'weight_decay': weight_decay,
                'lr': cls_embedding_lr
            })
        
        # Print parameter counts
        num_regular_decay = sum(p.numel() for p in regular_decay_params)
        num_regular_nodecay = sum(p.numel() for p in regular_nodecay_params)
        num_embedding_decay = sum(p.numel() for p in embedding_decay_params)
        num_embedding_nodecay = sum(p.numel() for p in embedding_nodecay_params)
        num_cls_embedding = sum(p.numel() for p in cls_embedding_params) if cls_embedding_params else 0
        
        print(f"Regular params (lr={learning_rate:.2e}): decay={num_regular_decay:,}, no_decay={num_regular_nodecay:,}")
        print(f"Embedding params (lr={embedding_lr:.2e}): decay={num_embedding_decay:,}, no_decay={num_embedding_nodecay:,}")
        if self.use_multi_cls and cls_embedding_params:
            cls_embedding_lr = learning_rate * self.config.cls_embedding_lr_scale
            print(f"Multi-CLS embedding params (lr={cls_embedding_lr:.2e}): {num_cls_embedding:,}")
        
        # Create AdamW optimizer and use the fused version if it is available
        fused_available = 'fused' in inspect.signature(torch.optim.AdamW).parameters
        use_fused = fused_available and device_type == "cuda"
        print(f"using fused AdamW: {use_fused}")
        optimizer = torch.optim.AdamW(optim_groups, betas=(0.9, 0.95), eps=1e-8, fused=use_fused)
        return optimizer
