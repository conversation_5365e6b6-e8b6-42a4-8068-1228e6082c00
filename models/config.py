import yaml
import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class AttentionConfig:
    """Configuration class for TimeMixedSelfAttention"""
    n_embd: int = 768
    n_head: int = 12
    n_head_4: int = 12
    n_layer: int = 12
    block_size: int = 1024
    layer_id: int = 0
    # Multi-CLS configuration
    use_multi_cls: bool = False
    n_cls_tokens: int = 16


@dataclass
class LoggingConfig:
    """Configuration class for training logging"""
    writer_dir_path: str = './log_encoder_pile_BGE/'
    run_group_name: str = 'encoder_mlm_124M_pile_20B'
    run_no: str = "pile_sampling_20B"
    log_dir: str = "log_encoder_mlm_pile_20B_pure_lower_special_tokens_lr_batchprob_new"
    log_file: str = "log_pile_20B_training.txt"


@dataclass
class DataConfig:
    """Configuration class for data loading"""
    tokenizer_path: str = "BAAI/bge-m3"
    dataset_paths: list = None
    target_tokens: int = 20_000_000_000
    dataset_name: str = "Pile"


@dataclass
class ModelConfig:
    """Configuration class for model architecture"""
    block_size: int = 1024  # max sequence length
    vocab_size: int = 50257  # base GPT2 vocab size (will be overridden by tokenizer)
    n_layer: int = 12  # number of layers
    n_head: int = 12  # number of heads
    n_embd: int = 768  # embedding dimension
    n_head_4: int = 12  # embedding dimension
    mask_prob: float = 0.2  # probability of masking tokens for MLM
    # Multi-CLS token configuration
    use_multi_cls: bool = False  # Choose between CLS+EOS (False) or multi-CLS (True)
    n_cls_tokens: int = 16  # Number of CLS tokens when use_multi_cls is True
    cls_embedding_lr_scale: float = 0.1  # Learning rate scale for CLS embeddings


@dataclass
class TrainingConfig:
    """Configuration class for training hyperparameters"""
    total_batch_size: int = 1048576  # 2**20, ~1M tokens
    micro_batch_size: int = 32  # B
    sequence_length: int = 1024  # T
    max_steps: int = 20000
    warmup_steps: int = 400
    max_lr: float = 2e-4
    min_lr: float = 2e-5
    weight_decay: float = 0.1
    use_compile: bool = True
    optimizer_lr: float = 6e-4
    embedding_lr_scale: float = 0.1

@dataclass
class WandbConfig:
    """Configuration class for Weights & Biases logging"""
    enabled: bool = True
    project: str = "encoder-mlm-ali"
    entity: Optional[str] = None
    name: Optional[str] = None
    tags: list = None
    notes: str = ""
    log_interval: int = 10
    watch_model: bool = True
    watch_log: str = "all"


def load_config_from_yaml(config_path: str) -> AttentionConfig:
    """Load configuration from YAML file"""
    if not os.path.exists(config_path):
        print(f"Warning: {config_path} not found, using default config")
        return AttentionConfig()
    
    with open(config_path, 'r') as f:
        config_dict = yaml.safe_load(f)
    
    # Extract model parameters from nested structure
    model_config = config_dict.get('model', {})
    
    return AttentionConfig(
        n_embd=model_config.get('n_embd', 768),
        n_head=model_config.get('n_head', 12),
        n_head_4=model_config.get('n_head_4', 12),
        n_layer=model_config.get('n_layer', 12),
        block_size=model_config.get('block_size', 1024),
        layer_id=0  # Default, will be set per layer
    )


def load_logging_config_from_yaml(config_path: str) -> LoggingConfig:
    """Load logging configuration from YAML file"""
    if not os.path.exists(config_path):
        print(f"Warning: {config_path} not found, using default logging config")
        return LoggingConfig()
    
    with open(config_path, 'r') as f:
        config_dict = yaml.safe_load(f)
    
    # Extract logging parameters from nested structure
    logging_config = config_dict.get('logging', {})
    
    return LoggingConfig(
        writer_dir_path=logging_config.get('writer_dir_path', './log_encoder_pile_BGE/'),
        run_group_name=logging_config.get('run_group_name', 'encoder_mlm_124M_pile_20B'),
        run_no=logging_config.get('run_no', 'pile_sampling_20B'),
        log_dir=logging_config.get('log_dir', 'log_encoder_mlm_pile_20B_pure_lower_special_tokens_lr_batchprob_new'),
        log_file=logging_config.get('log_file', 'log_pile_20B_training.txt')
    )


def load_data_config_from_yaml(config_path: str) -> DataConfig:
    """Load data configuration from YAML file"""
    if not os.path.exists(config_path):
        print(f"Warning: {config_path} not found, using default data config")
        return DataConfig()
    
    with open(config_path, 'r') as f:
        config_dict = yaml.safe_load(f)
    
    # Extract data parameters from nested structure
    data_config = config_dict.get('data', {})
    
    return DataConfig(
        tokenizer_path=data_config.get('tokenizer_path', "BAAI/bge-m3"),
        dataset_paths=data_config.get('dataset_paths', ["/s2_nfs/pile/OpenWebText2"]),
        target_tokens=data_config.get('target_tokens', 20_000_000_000),
        dataset_name=data_config.get('dataset_name', "Pile")
    )


def load_training_config_from_yaml(config_path: str) -> TrainingConfig:
    """Load training configuration from YAML file"""
    if not os.path.exists(config_path):
        print(f"Warning: {config_path} not found, using default training config")
        return TrainingConfig()
    
    with open(config_path, 'r') as f:
        config_dict = yaml.safe_load(f)
    
    # Extract training parameters from nested structure
    training_config = config_dict.get('training', {})
    
    return TrainingConfig(
        total_batch_size=training_config.get('total_batch_size', 1048576),
        micro_batch_size=training_config.get('micro_batch_size', 32),
        sequence_length=training_config.get('sequence_length', 1024),
        max_steps=training_config.get('max_steps', 20000),
        warmup_steps=training_config.get('warmup_steps', 400),
        max_lr=float(training_config.get('max_lr', 0.0002)),
        min_lr=float(training_config.get('min_lr', 0.00002)),
        weight_decay=training_config.get('weight_decay', 0.1),
        use_compile=training_config.get('use_compile', True),
        optimizer_lr=float(training_config.get('optimizer_lr', 0.0006)),
        embedding_lr_scale=float(training_config.get('embedding_lr_scale', 0.1))
    )


def load_model_config_from_yaml(config_path: str) -> ModelConfig:
    """Load model configuration from YAML file"""
    if not os.path.exists(config_path):
        print(f"Warning: {config_path} not found, using default model config")
        return ModelConfig()
    
    with open(config_path, 'r') as f:
        config_dict = yaml.safe_load(f)
    
    # Extract model parameters from nested structure
    model_config = config_dict.get('model', {})
    
    return ModelConfig(
        block_size=model_config.get('block_size', 1024),
        vocab_size=model_config.get('vocab_size', 50257),
        n_layer=model_config.get('n_layer', 12),
        n_head=model_config.get('n_head', 12),
        n_embd=model_config.get('n_embd', 768),
        n_head_4=model_config.get('n_head_4', 12),
        mask_prob=model_config.get('mask_prob', 0.2),
        use_multi_cls=model_config.get('use_multi_cls', False),
        n_cls_tokens=model_config.get('n_cls_tokens', 16),
        cls_embedding_lr_scale=model_config.get('cls_embedding_lr_scale', 0.1)
    )


def load_all_configs_from_yaml(config_path: str) -> tuple[LoggingConfig, DataConfig, TrainingConfig, ModelConfig, WandbConfig]:
    """Load all configurations from YAML file in a single read operation"""
    if not os.path.exists(config_path):
        print(f"Warning: {config_path} not found, using default configs")
        return LoggingConfig(), DataConfig(), TrainingConfig(), ModelConfig(), WandbConfig()
    
    with open(config_path, 'r') as f:
        config_dict = yaml.safe_load(f)
    
    # Extract all sections
    logging_config = config_dict.get('logging', {})
    data_config = config_dict.get('data', {})
    training_config = config_dict.get('training', {})
    model_config = config_dict.get('model', {})
    wandb_config = config_dict.get('wandb', {})
    
    return (
        LoggingConfig(
            writer_dir_path=logging_config.get('writer_dir_path', "./log_encoder_pile_BGE/"),
            run_group_name=logging_config.get('run_group_name', "encoder_mlm_124M_pile_20B"),
            run_no=logging_config.get('run_no', "pile_sampling_20B"),
            log_dir=logging_config.get('log_dir', "log_encoder_mlm_pile_20B_pure_lower_special_tokens_lr_batchprob_new"),
            log_file=logging_config.get('log_file', "log_pile_20B_training.txt")
        ),
        DataConfig(
            tokenizer_path=data_config.get('tokenizer_path', "BAAI/bge-m3"),
            dataset_paths=data_config.get('dataset_paths', ["/s2_nfs/pile/OpenWebText2"]),
            target_tokens=data_config.get('target_tokens', 20_000_000_000),
            dataset_name=data_config.get('dataset_name', "Pile")
        ),
        TrainingConfig(
            total_batch_size=training_config.get('total_batch_size', 1048576),
            micro_batch_size=training_config.get('micro_batch_size', 32),
            sequence_length=training_config.get('sequence_length', 1024),
            max_steps=training_config.get('max_steps', 20000),
            warmup_steps=training_config.get('warmup_steps', 400),
            max_lr=float(training_config.get('max_lr', 0.0002)),
            min_lr=float(training_config.get('min_lr', 0.00002)),
            weight_decay=training_config.get('weight_decay', 0.1),
            use_compile=training_config.get('use_compile', True),
            optimizer_lr=float(training_config.get('optimizer_lr', 0.0006)),
            embedding_lr_scale=training_config.get('embedding_lr_scale', 0.1)
        ),
        ModelConfig(
            block_size=model_config.get('block_size', 1024),
            vocab_size=model_config.get('vocab_size', 50257),
            n_layer=model_config.get('n_layer', 12),
            n_head=model_config.get('n_head', 12),
            n_embd=model_config.get('n_embd', 768),
            n_head_4=model_config.get('n_head_4', 12),
            mask_prob=model_config.get('mask_prob', 0.2),
            use_multi_cls=model_config.get('use_multi_cls', False),
            n_cls_tokens=model_config.get('n_cls_tokens', 16),
            cls_embedding_lr_scale=model_config.get('cls_embedding_lr_scale', 0.1)
        ),
        WandbConfig(
            enabled=wandb_config.get('enabled', True),
            project=wandb_config.get('project', "encoder-mlm-ali"),
            entity=wandb_config.get('entity'),
            name=wandb_config.get('name'),
            tags=wandb_config.get('tags', []),
            notes=wandb_config.get('notes', ""),
            log_interval=wandb_config.get('log_interval', 10),
            watch_model=wandb_config.get('watch_model', True),
            watch_log=wandb_config.get('watch_log', "all")
        )
    )


def create_layer_config(config_path: str, layer_id: int) -> AttentionConfig:
    """Load config from YAML and create a layer-specific config with the given layer_id"""
    base_config = load_config_from_yaml(config_path)
    return AttentionConfig(
        n_embd=base_config.n_embd,
        n_head=base_config.n_head,
        n_head_4=base_config.n_head_4,
        n_layer=base_config.n_layer,
        block_size=base_config.block_size,
        layer_id=layer_id
    )


def create_layer_config_from_base(base_config: AttentionConfig, layer_id: int) -> AttentionConfig:
    """Create a layer-specific config from an existing base config (no YAML loading)"""
    return AttentionConfig(
        n_embd=base_config.n_embd,
        n_head=base_config.n_head,
        n_head_4=base_config.n_head_4,
        n_layer=base_config.n_layer,
        block_size=base_config.block_size,
        layer_id=layer_id,
        use_multi_cls=getattr(base_config, 'use_multi_cls', False),
        n_cls_tokens=getattr(base_config, 'n_cls_tokens', 16)
    )
