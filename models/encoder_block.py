import torch.nn as nn
import copy
from .attention import TimeMixedSelfAttention
from .encoder_mlp import EncoderMLP
from .config import create_layer_config_from_base


class EncoderBlock(nn.Module):
    """Transformer encoder block combining attention and MLP layers"""

    def __init__(self, config, attention_config, layer_id):
        super().__init__()
        self.ln_1 = nn.LayerNorm(config.n_embd)
        
        # Create layer-specific attention config (no YAML loading here)
        layer_attention_config = create_layer_config_from_base(attention_config, layer_id)
        
        # Pass multi-CLS configuration to attention layer
        if hasattr(config, 'use_multi_cls'):
            layer_attention_config.use_multi_cls = config.use_multi_cls
            layer_attention_config.n_cls_tokens = config.n_cls_tokens
        
        # Create layer-specific config for MLP (using copy to avoid mutation)
        layer_mlp_config = copy.deepcopy(config)
        layer_mlp_config.layer_id = layer_id
        
        self.attn = TimeMixedSelfAttention(layer_attention_config)
        self.ln_2 = nn.LayerNorm(config.n_embd)
        self.mlp = EncoderMLP(layer_mlp_config)

    def forward(self, x, attention_mask=None):
        x = x + self.attn(self.ln_1(x), attention_mask=attention_mask)
        x = x + self.mlp(self.ln_2(x), attention_mask=attention_mask)
        return x
