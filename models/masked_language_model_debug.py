import torch
import torch.nn as nn
from torch.nn import functional as F
import inspect

from .encoder_block import EncoderBlock
from .config import load_config_from_yaml


class MaskedLanguageModelDebug(nn.Module):
    """Debugging version of MaskedLanguageModel with comprehensive gradient and NaN tracking"""

    def __init__(self, config, tokenizer=None):
        super().__init__()
        self.config = config
        
        # Load attention config once for all layers (optimization)
        self.attention_config = load_config_from_yaml("config.yaml")
        
        # If tokenizer is provided, use its vocabulary and special tokens
        if tokenizer is not None:
            self.tokenizer = tokenizer
            effective_vocab_size = len(tokenizer)
            self.mask_token_id = tokenizer.mask_token_id
            self.pad_token_id = tokenizer.pad_token_id
            self.cls_token_id = tokenizer.cls_token_id
            self.eos_token_id = tokenizer.eos_token_id
        else:
            # Fallback to original behavior
            self.mask_token_id = config.vocab_size      # id for [MASK]
            self.pad_token_id = config.vocab_size + 1   # id for [PAD]
            self.cls_token_id = config.vocab_size + 2   # id for [CLS]
            self.eos_token_id = config.vocab_size + 3   # id for [EOS]
            effective_vocab_size = config.vocab_size + 4

        self.transformer = nn.ModuleDict(dict(
            wte = nn.Embedding(effective_vocab_size, config.n_embd),
            h = nn.ModuleList([EncoderBlock(config, self.attention_config, layer_id=i) for i in range(config.n_layer)]),
            ln_f = nn.LayerNorm(config.n_embd),
        ))
        # Gate projection for hidden state-based interpolation (d -> d)
        self.hidden_gate = nn.Linear(config.n_embd, config.n_embd, bias=False)
        # MLM head uses interpolated embeddings (d -> vocab)
        self.lm_head = nn.Linear(config.n_embd, effective_vocab_size, bias=False)

        # Enable gradient checkpointing for memory efficiency
        self.gradient_checkpointing = False

        # init params
        self.apply(self._init_weights)

    def enable_gradient_checkpointing(self):
        """Enable gradient checkpointing to save memory"""
        self.gradient_checkpointing = True
        
    def disable_gradient_checkpointing(self):
        """Disable gradient checkpointing"""
        self.gradient_checkpointing = False

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            std = 0.02
            if hasattr(module, 'NANOGPT_SCALE_INIT'):
                std *= (2 * self.config.n_layer) ** -0.5
            torch.nn.init.normal_(module.weight, mean=0.0, std=std)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)

    def check_tensor_debug(self, tensor, name, step_name):
        """Debug helper to check tensor properties"""
        if tensor is None:
            print(f"DEBUG: {step_name} - {name} is None!")
            return False
            
        has_nan = torch.isnan(tensor).any() if tensor.dtype.is_floating_point else torch.tensor(False)
        has_inf = torch.isinf(tensor).any() if tensor.dtype.is_floating_point else torch.tensor(False)
        requires_grad = tensor.requires_grad
        has_grad_fn = tensor.grad_fn is not None
        
        # Handle different tensor types for statistics
        if tensor.dtype.is_floating_point or tensor.dtype.is_complex:
            min_val = tensor.min().item()
            max_val = tensor.max().item()
            mean_val = tensor.mean().item()
            stats_str = f"min={min_val:.6f}, max={max_val:.6f}, mean={mean_val:.6f}"
        else:
            # For integer tensors, just show min/max
            min_val = tensor.min().item()
            max_val = tensor.max().item()
            stats_str = f"min={min_val}, max={max_val}"
        
        print(f"DEBUG: {step_name} - {name}: shape={tensor.shape}, requires_grad={requires_grad}, has_grad_fn={has_grad_fn}, has_nan={has_nan}, has_inf={has_inf}, {stats_str}")
        
        if has_nan or has_inf:
            print(f"WARNING: {step_name} - {name} contains NaN or Inf values!")
            return False
        return True

    def forward(self, idx, attention_mask=None, labels=None):
        print(f"\nDEBUG: Starting forward pass")
        
        # idx is of shape (B, T)
        B, T = idx.size()
        assert T <= self.config.block_size, f"Cannot forward sequence of length {T}, block size is only {self.config.block_size}"
        
        # Debug input tensors
        self.check_tensor_debug(idx, "input_idx", "INPUT")
        if attention_mask is not None:
            self.check_tensor_debug(attention_mask, "attention_mask", "INPUT")
        if labels is not None:
            self.check_tensor_debug(labels, "labels", "INPUT")
        
        # token embeddings
        tok_emb = self.transformer.wte(idx) # (B, T, n_embd)
        self.check_tensor_debug(tok_emb, "token_embeddings", "EMBEDDING")
        
        x = tok_emb
        # forward the blocks of the transformer
        for i, block in enumerate(self.transformer.h):
            if self.gradient_checkpointing:
                x = torch.utils.checkpoint.checkpoint(block, x, attention_mask)
            else:
                x = block(x, attention_mask=attention_mask)
            self.check_tensor_debug(x, f"block_{i}_output", f"TRANSFORMER_BLOCK_{i}")
            
        # forward the final layernorm
        x = self.transformer.ln_f(x)
        self.check_tensor_debug(x, "final_layernorm_output", "FINAL_LAYERNORM")
        
        # Extract CLS and EOS token embeddings
        cls = x[:, 0, :]  # (B, d) - CLS token at position 0
        self.check_tensor_debug(cls, "cls_tokens", "CLS_EXTRACTION")
        
        # Find EOS token positions (last non-pad token before padding)
        if attention_mask is not None:
            # Find the last non-pad position for each sequence
            seq_lengths = attention_mask.sum(dim=1)  # (B,)
            self.check_tensor_debug(seq_lengths, "seq_lengths", "EOS_CALCULATION")
            
            eos_positions = seq_lengths - 1  # EOS should be at the last valid position
            self.check_tensor_debug(eos_positions, "eos_positions", "EOS_CALCULATION")
            
            # Extract EOS tokens using advanced indexing
            batch_indices = torch.arange(B, device=x.device)
            eos = x[batch_indices, eos_positions, :]  # (B, d)
        else:
            # Fallback: assume EOS is at position T-1
            eos = x[:, -1, :]  # (B, d)
        
        self.check_tensor_debug(eos, "eos_tokens", "EOS_EXTRACTION")
        
        # For MLM loss, we only need to compute logits for positions that have labels != -100
        if labels is not None:
            # Find positions where labels != -100 (i.e., masked positions)
            mask_positions = (labels != -100)  # (B, T)
            print(f"DEBUG: Found {mask_positions.sum().item()} masked positions out of {B*T} total positions")
            
            if mask_positions.any():
                # Get indices of masked positions
                batch_indices, pos_indices = torch.where(mask_positions)
                print(f"DEBUG: Processing {len(batch_indices)} masked positions")
                
                # Create interpolation weights only for masked positions
                if attention_mask is not None:
                    # Use actual EOS positions for normalization
                    eos_pos_for_masked = eos_positions[batch_indices].float()  # (num_masked,)
                    self.check_tensor_debug(eos_pos_for_masked, "eos_pos_for_masked", "INTERPOLATION_WEIGHTS")
                    
                    # Normalize positions: alpha = position / eos_position
                    alpha_masked = pos_indices.float() / torch.clamp(eos_pos_for_masked, min=1.0)  # (num_masked,)
                    alpha_masked = torch.clamp(alpha_masked, 0.0, 1.0)  # (num_masked,)
                else:
                    # Fallback: normalize by T-1
                    alpha_masked = pos_indices.float() / (T - 1) if T > 1 else torch.zeros_like(pos_indices.float())
                
                self.check_tensor_debug(alpha_masked, "alpha_masked", "INTERPOLATION_WEIGHTS")
                
                # Get CLS and EOS for the relevant batches
                cls_for_masked = cls[batch_indices]  # (num_masked, d)
                eos_for_masked = eos[batch_indices]  # (num_masked, d)
                self.check_tensor_debug(cls_for_masked, "cls_for_masked", "INTERPOLATION_INPUTS")
                self.check_tensor_debug(eos_for_masked, "eos_for_masked", "INTERPOLATION_INPUTS")
                
                # Get hidden states for masked positions
                hidden_for_masked = x[batch_indices, pos_indices, :]  # (num_masked, d)
                self.check_tensor_debug(hidden_for_masked, "hidden_for_masked", "INTERPOLATION_INPUTS")
                
                # Position-wise interpolation only for masked positions
                alpha_expanded = alpha_masked.unsqueeze(-1)  # (num_masked, 1)
                self.check_tensor_debug(alpha_expanded, "alpha_expanded", "POSITION_INTERPOLATION")
                
                pos_interpolated_masked = (1 - alpha_expanded) * cls_for_masked + alpha_expanded * eos_for_masked  # (num_masked, d)
                self.check_tensor_debug(pos_interpolated_masked, "pos_interpolated_masked", "POSITION_INTERPOLATION")
                
                # Hidden state-based interpolation using gate
                gate_input = self.hidden_gate(hidden_for_masked)
                self.check_tensor_debug(gate_input, "gate_input", "HIDDEN_INTERPOLATION")
                
                gate_weights = torch.sigmoid(gate_input)  # (num_masked, d)
                self.check_tensor_debug(gate_weights, "gate_weights", "HIDDEN_INTERPOLATION")
                
                hidden_interpolated_masked = cls_for_masked * gate_weights + eos_for_masked * (1 - gate_weights)  # (num_masked, d)
                self.check_tensor_debug(hidden_interpolated_masked, "hidden_interpolated_masked", "HIDDEN_INTERPOLATION")
                
                # Combine both interpolations using mean
                final_interpolated_masked = (pos_interpolated_masked + hidden_interpolated_masked) / 2.0  # (num_masked, d)
                self.check_tensor_debug(final_interpolated_masked, "final_interpolated_masked", "FINAL_INTERPOLATION")
                
                # Compute logits only for masked positions
                logits_masked = self.lm_head(final_interpolated_masked)  # (num_masked, vocab_size)
                self.check_tensor_debug(logits_masked, "logits_masked", "LOGITS_COMPUTATION")
                
                # Create full logits tensor (we only need masked positions for loss)
                logits = torch.zeros(B, T, logits_masked.size(-1), device=x.device, dtype=logits_masked.dtype)
                logits[batch_indices, pos_indices] = logits_masked
                self.check_tensor_debug(logits, "full_logits", "LOGITS_FINAL")
            else:
                # No masked positions, create empty logits
                print("DEBUG: No masked positions found, creating zero logits")
                logits = torch.zeros(B, T, self.lm_head.out_features, device=x.device, dtype=x.dtype)
                # Ensure gradients are enabled
                logits.requires_grad_(True)
        else:
            print("DEBUG: No labels provided, computing full interpolation for inference")
            # For inference/evaluation, we might need full interpolation (fallback)
            # Position-wise interpolation of CLS and EOS for all positions
            positions = torch.arange(T, device=x.device).float()  # (T,)
            
            if attention_mask is not None:
                # Use actual EOS positions for normalization
                eos_positions_expanded = eos_positions.unsqueeze(1).float()  # (B, 1)
                alpha = positions.unsqueeze(0) / torch.clamp(eos_positions_expanded, min=1.0)  # (B, T)
                alpha = torch.clamp(alpha, 0.0, 1.0)  # (B, T)
                alpha = alpha.unsqueeze(-1)  # (B, T, 1)
            else:
                alpha = positions / (T - 1) if T > 1 else torch.zeros_like(positions)  # (T,)
                alpha = alpha.unsqueeze(0).unsqueeze(-1)  # (1, T, 1)
            
            # Expand CLS and EOS to all positions
            cls_expanded = cls.unsqueeze(1).expand(-1, T, -1)  # (B, T, d)
            eos_expanded = eos.unsqueeze(1).expand(-1, T, -1)  # (B, T, d)
            
            # Position-wise interpolation: (1-alpha) * CLS + alpha * EOS
            pos_interpolated = (1 - alpha) * cls_expanded + alpha * eos_expanded  # (B, T, d)
            
            # Hidden state-based interpolation using gate
            gate_weights = torch.sigmoid(self.hidden_gate(x))  # (B, T, d)
            hidden_interpolated = cls_expanded * gate_weights + eos_expanded * (1 - gate_weights)  # (B, T, d)
            
            # Combine both interpolations using mean
            final_interpolated = (pos_interpolated + hidden_interpolated) / 2.0  # (B, T, d)
            
            # MLM head: use combined interpolated embeddings for predictions
            logits = self.lm_head(final_interpolated)  # (B, T, vocab_size)
            self.check_tensor_debug(logits, "inference_logits", "INFERENCE_LOGITS")
        
        loss = None
        # MLM loss: labels should be -100 for non-masked positions (and PAD positions)
        if labels is not None:
            print(f"DEBUG: Computing cross entropy loss")
            self.check_tensor_debug(logits.view(-1, logits.size(-1)), "logits_flattened", "LOSS_COMPUTATION")
            self.check_tensor_debug(labels.view(-1), "labels_flattened", "LOSS_COMPUTATION")
            
            # Check if any logits require gradients
            flattened_logits = logits.view(-1, logits.size(-1))
            print(f"DEBUG: Flattened logits requires_grad: {flattened_logits.requires_grad}")
            print(f"DEBUG: Flattened logits grad_fn: {flattened_logits.grad_fn}")
            
            loss = F.cross_entropy(flattened_logits, labels.view(-1), ignore_index=-100)
            self.check_tensor_debug(loss, "cross_entropy_loss", "LOSS_FINAL")
            
        print(f"DEBUG: Forward pass completed\n")
        return logits, loss

    def configure_optimizers(self, weight_decay, learning_rate, device_type, embedding_lr_scale=0.1):
        # start with all of the candidate parameters (that require grad)
        param_dict = {pn: p for pn, p in self.named_parameters()}
        param_dict = {pn: p for pn, p in param_dict.items() if p.requires_grad}
        
        print(f"DEBUG: Parameters requiring gradients:")
        for pn, p in param_dict.items():
            print(f"  {pn}: shape={p.shape}, requires_grad={p.requires_grad}")
        
        # Separate parameters into different groups based on type and learning rate
        embedding_decay_params = []
        embedding_nodecay_params = []
        regular_decay_params = []
        regular_nodecay_params = []
        
        for pn, p in param_dict.items():
            # Check if this is an embedding parameter (specifically CLS/EOS tokens)
            is_embedding = 'transformer.wte' in pn
            
            if p.dim() >= 2:
                if is_embedding:
                    embedding_decay_params.append(p)
                else:
                    regular_decay_params.append(p)
            else:
                if is_embedding:
                    embedding_nodecay_params.append(p)
                else:
                    regular_nodecay_params.append(p)
        
        # Create optimizer groups with different learning rates
        optim_groups = []
        
        # Regular parameters (full learning rate)
        if regular_decay_params:
            optim_groups.append({
                'params': regular_decay_params, 
                'weight_decay': weight_decay,
                'lr': learning_rate
            })
        if regular_nodecay_params:
            optim_groups.append({
                'params': regular_nodecay_params, 
                'weight_decay': 0.0,
                'lr': learning_rate
            })
        
        # Embedding parameters (reduced learning rate)
        embedding_lr = learning_rate * embedding_lr_scale
        if embedding_decay_params:
            optim_groups.append({
                'params': embedding_decay_params, 
                'weight_decay': weight_decay,
                'lr': embedding_lr
            })
        if embedding_nodecay_params:
            optim_groups.append({
                'params': embedding_nodecay_params, 
                'weight_decay': 0.0,
                'lr': embedding_lr
            })
        
        # Print parameter counts
        num_regular_decay = sum(p.numel() for p in regular_decay_params)
        num_regular_nodecay = sum(p.numel() for p in regular_nodecay_params)
        num_embedding_decay = sum(p.numel() for p in embedding_decay_params)
        num_embedding_nodecay = sum(p.numel() for p in embedding_nodecay_params)
        
        print(f"Regular params (lr={learning_rate:.2e}): decay={num_regular_decay:,}, no_decay={num_regular_nodecay:,}")
        print(f"Embedding params (lr={embedding_lr:.2e}): decay={num_embedding_decay:,}, no_decay={num_embedding_nodecay:,}")
        
        # Create AdamW optimizer and use the fused version if it is available
        fused_available = 'fused' in inspect.signature(torch.optim.AdamW).parameters
        use_fused = fused_available and device_type == "cuda"
        print(f"using fused AdamW: {use_fused}")
        optimizer = torch.optim.AdamW(optim_groups, betas=(0.9, 0.95), eps=1e-8, fused=use_fused)
        return optimizer
