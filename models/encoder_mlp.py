import torch
import torch.nn as nn
import math


class EncoderMLP(nn.Module):

    def __init__(self, config):
        super().__init__()
        
        # SwiGLU approach: gate and value with expanded intermediate size for SwiGLU
        # SwiGLU typically uses 8/3 * hidden_dim to maintain parameter count
        self.hidden_dim = int(8 * config.n_embd / 3)
        
        # SwiGLU projections: gate (gets swish) and value
        self.c_fc_gate = nn.Linear(config.n_embd, self.hidden_dim, bias=False)
        self.c_fc_value = nn.Linear(config.n_embd, self.hidden_dim, bias=False)
        self.c_proj = nn.Linear(self.hidden_dim, config.n_embd, bias=False)
        self.c_proj.NANOGPT_SCALE_INIT = 1

        # Time-mixing parameters
        self.layer_id = getattr(config, 'layer_id', 0)  # Will be set per layer
        self.n_layer = config.n_layer
        self.n_embd = config.n_embd
        self.time_shift = nn.ZeroPad2d((0, 0, 1, -1))

        with torch.no_grad():
            ratio_0_to_1 = self.layer_id / (self.n_layer - 1) if self.n_layer > 1 else 0
            ratio_1_to_almost0 = 1.0 - (self.layer_id / self.n_layer) if self.n_layer > 0 else 1.0
            ddd = torch.ones(1, 1, self.n_embd)
            for i in range(self.n_embd):
                ddd[0, 0, i] = i / self.n_embd
            
            # Base time mixing parameter
            self.time_maa_x = nn.Parameter(1.0 - torch.pow(ddd, 0.6 * ratio_1_to_almost0 ** 0.9))
            
            # Separate time mixing parameters for gate and value (SwiGLU style)
            self.time_maa_gate = nn.Parameter(1.0 - torch.pow(ddd, 0.3 * ratio_1_to_almost0))
            self.time_maa_value = nn.Parameter(1.0 - torch.pow(ddd, 0.5 * ratio_1_to_almost0))

            # LoRA components for gate and value
            D_MIX_LORA = 32
            self.time_maa_w1 = nn.Parameter(torch.zeros(self.n_embd, D_MIX_LORA * 2))
            self.time_maa_w2 = nn.Parameter(self.ortho_init(torch.zeros(2, D_MIX_LORA, self.n_embd), 0.1))

    def ortho_init(self, x: torch.Tensor, scale: float) -> torch.Tensor:
        with torch.no_grad():
            shape = x.shape
            if len(shape) == 2:
                gain = math.sqrt(max(1.0, shape[0] / shape[1]))
                nn.init.orthogonal_(x, gain=gain * scale)
            elif len(shape) == 3:
                gain = math.sqrt(max(1.0, shape[1] / shape[2]))
                for i in range(shape[0]):
                    nn.init.orthogonal_(x[i], gain=gain * scale)
            else:
                raise ValueError("Unsupported tensor shape for ortho_init.")
            return x

    def swish(self, x):
        """Swish activation: x * sigmoid(x)"""
        return x * torch.sigmoid(x)

    def forward(self, x, attention_mask=None):
        B, T, C = x.shape
        
        # RWKV time-mixing logic with SwiGLU (bidirectional neighbors average)
        x_left = torch.zeros_like(x)
        x_left[:, 1:, :] = x[:, :-1, :]
        x_right = torch.zeros_like(x)
        x_right[:, :-1, :] = x[:, 1:, :]
        if attention_mask is not None:
            m = attention_mask.to(dtype=x.dtype).unsqueeze(-1)
            left_mask = torch.zeros_like(m)
            left_mask[:, 1:, :] = m[:, :-1, :]
            right_mask = torch.zeros_like(m)
            right_mask[:, :-1, :] = m[:, 1:, :]
            numer = x_left * left_mask + x_right * right_mask
            denom = left_mask + right_mask
            x_neighbors = torch.where(denom > 0, numer / torch.clamp(denom, min=1.0), x)
        else:
            x_neighbors = 0.5 * (x_left + x_right)
        xx = x_neighbors - x
        x_base = x + xx * self.time_maa_x
        
        # LoRA components for gate and value
        lora_pre_bmm = torch.tanh(x_base @ self.time_maa_w1).view(B * T, 2, -1).transpose(0, 1)
        lora_components = torch.bmm(lora_pre_bmm, self.time_maa_w2).view(2, B, T, C)
        lora_gate, lora_value = lora_components.unbind(dim=0)
        
        # Separate x_intermediate for gate and value (SwiGLU style)
        x_gate = x + xx * (self.time_maa_gate + lora_gate)
        x_value = x + xx * (self.time_maa_value + lora_value)
        
        # SwiGLU: Swish(gate) * value
        gate = self.c_fc_gate(x_gate)      # gate projection
        value = self.c_fc_value(x_value)   # value projection
        
        # Apply SwiGLU: Swish(gate) ⊙ value
        y = self.swish(gate) * value
        
        # Project back to original dimension
        y = self.c_proj(y)
        
        return y
