import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from .config import AttentionConfig


class TimeMixedSelfAttention(nn.Module):
    """Time-mixed self-attention mechanism with RoPE and bidirectional time mixing"""

    def __init__(self, config: AttentionConfig):
        super().__init__()
        assert config.n_embd % config.n_head == 0
        
        # Store config parameters
        self.n_head = config.n_head
        self.n_head_4 = config.n_head_4
        self.n_embd = config.n_embd
        self.block_size = config.block_size
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.dtype = torch.bfloat16
        
        # Multi-CLS configuration (will be set by the model if needed)
        self.use_multi_cls = getattr(config, 'use_multi_cls', False)
        self.n_cls_tokens = getattr(config, 'n_cls_tokens', 16)
        
        # Time-mixing parameters (adapted from gptrwkv-2)
        self.layer_id = getattr(config, 'layer_id', 0)  # Will be set per layer
        self.n_layer = config.n_layer

        # Standard RoPE setup
        head_dim = self.n_embd // self.n_head
        inv_freq = 1.0 / (10000.0 ** (torch.arange(0, head_dim, 2).float() / head_dim))
        self.register_buffer('inv_freq', inv_freq)
        
        # Time mixing parameters initialization
        with torch.no_grad():
            ratio_0_to_1 = self.layer_id / (self.n_layer - 1) if self.n_layer > 1 else 0
            ratio_1_to_almost0 = 1.0 - (self.layer_id / self.n_layer) if self.n_layer > 0 else 1.0         
            ddd = torch.ones(1, 1, self.n_embd)
            for i in range(self.n_embd):
                ddd[0, 0, i] = i / self.n_embd

            self.time_maa_x = nn.Parameter(1.0 - torch.pow(ddd, 0.6 * ratio_1_to_almost0 ** 0.9))
            self.time_maa_q = nn.Parameter(1.0 - torch.pow(ddd, 0.9 * ratio_1_to_almost0)) 
            self.time_maa_k = nn.Parameter(1.0 - (torch.pow(ddd, 0.9 * ratio_1_to_almost0) + 0.4 * ratio_0_to_1))
            self.time_maa_v = nn.Parameter(1.0 - (torch.pow(ddd, 0.4 * ratio_1_to_almost0) + 0.6 * ratio_0_to_1))

            D_MIX_LORA = 28 
            self.time_maa_w1 = nn.Parameter(torch.zeros(self.n_embd, D_MIX_LORA * 3))
            self.time_maa_w2 = nn.Parameter(self.ortho_init(torch.zeros(3, D_MIX_LORA, self.n_embd), 0.1))

        self.time_shift = nn.ZeroPad2d((0, 0, 1, -1))
        
        # Linear projections
        self.query = nn.Linear(self.n_embd, self.n_embd, bias=False)
        self.key = nn.Linear(self.n_embd, self.n_embd, bias=False)
        self.value = nn.Linear(self.n_embd, self.n_embd, bias=False)
        self.c_proj = nn.Linear(self.n_embd, self.n_embd, bias=False)
        self.c_proj.NANOGPT_SCALE_INIT = 1

    def ortho_init(self, x: torch.Tensor, scale: float) -> torch.Tensor:
        with torch.no_grad():
            shape = x.shape
            if len(shape) == 2:
                gain = math.sqrt(max(1.0, shape[0] / shape[1]))
                nn.init.orthogonal_(x, gain=gain * scale)
            elif len(shape) == 3:
                gain = math.sqrt(max(1.0, shape[1] / shape[2]))
                for i in range(shape[0]):
                    nn.init.orthogonal_(x[i], gain=gain * scale)
            else:
                raise ValueError("Unsupported tensor shape for ortho_init.")
            return x

    def rotate_half(self, x):
        """Rotates half the hidden dims of the input."""
        x1, x2 = x.chunk(2, dim=-1)
        return torch.cat((-x2, x1), dim=-1)

    def apply_rotary_pos_emb(self, q, k, cos, sin):
        """Apply rotary position embeddings to q and k tensors"""
        q_embed = (q * cos) + (self.rotate_half(q) * sin)
        k_embed = (k * cos) + (self.rotate_half(k) * sin)
        return q_embed, k_embed

    def forward(self, x, attention_mask=None):
        B, T, C = x.size()
        H = self.n_head
        head_dim = C // H
        
        # Time-mixing logic (bidirectional): use average of neighbors (x(t-1)+x(t+1))/2
        x_left = torch.zeros_like(x)
        x_left[:, 1:, :] = x[:, :-1, :]
        x_right = torch.zeros_like(x)
        x_right[:, :-1, :] = x[:, 1:, :]
        if attention_mask is not None:
            m = attention_mask.to(dtype=x.dtype).unsqueeze(-1)
            left_mask = torch.zeros_like(m)
            left_mask[:, 1:, :] = m[:, :-1, :]
            right_mask = torch.zeros_like(m)
            right_mask[:, :-1, :] = m[:, 1:, :]
            numer = x_left * left_mask + x_right * right_mask
            denom = left_mask + right_mask
            x_neighbors = torch.where(denom > 0, numer / torch.clamp(denom, min=1.0), x)
        else:
            x_neighbors = 0.5 * (x_left + x_right)
        xx = x_neighbors - x
        x_base = x + xx * self.time_maa_x
        lora_pre_bmm = torch.tanh(x_base @ self.time_maa_w1).view(B * T, 3, -1).transpose(0, 1)
        lora_components = torch.bmm(lora_pre_bmm, self.time_maa_w2).view(3, B, T, C)
        lora_q, lora_k, lora_v = lora_components.unbind(dim=0)
        
        xq_intermediate = x + xx * (self.time_maa_q + lora_q)
        xk_intermediate = x + xx * (self.time_maa_k + lora_k)
        xv_intermediate = x + xx * (self.time_maa_v + lora_v)

        # Projections
        q = self.query(xq_intermediate).view(B, T, H, head_dim).transpose(1, 2)
        k = self.key(xk_intermediate).view(B, T, H, head_dim).transpose(1, 2)
        v = self.value(xv_intermediate).view(B, T, H, head_dim).transpose(1, 2)

        # RoPE position embeddings with multi-CLS support
        seq_len = T
        
        # Check if we should use custom multi-CLS positioning
        if self.use_multi_cls and attention_mask is not None:
            # Custom RoPE positioning for multi-CLS tokens (from standalone file)
            seq_lengths = attention_mask.sum(dim=1)  # (B,)
            max_effective_len = seq_lengths.max().item()
            
            # Fast distributed CLS positioning (vectorized)
            position_ids = torch.zeros(B, T, device=x.device, dtype=torch.float)
            
            # Efficient vectorized computation for all batches
            content_lengths = seq_lengths - self.n_cls_tokens  # (B,)
            max_content_len = content_lengths.max().item()
            
            # CLS positions: distributed from 0 to content_len-1
            cls_indices = torch.arange(self.n_cls_tokens, device=x.device, dtype=torch.float)  # [0,1,2,...,15]
            if self.n_cls_tokens > 1:
                cls_positions = cls_indices * (max_content_len - 1) / (self.n_cls_tokens - 1)
            else:
                cls_positions = torch.zeros_like(cls_indices)
            
            # Broadcast CLS positions to all batches
            position_ids[:, :self.n_cls_tokens] = cls_positions.unsqueeze(0)  # (B, n_cls_tokens)
            
            # Content positions: 0 to content_len-1 for each batch
            for b in range(B):
                content_len = content_lengths[b].item()
                if content_len > 0:
                    content_positions = torch.arange(content_len, device=x.device, dtype=torch.float)
                    position_ids[b, self.n_cls_tokens:self.n_cls_tokens+content_len] = content_positions
        else:
            # Standard sequential positioning
            position_ids = torch.arange(seq_len, device=x.device, dtype=torch.float).unsqueeze(0).expand(B, -1)
        
        # Apply RoPE (vectorized, no loops)
        cos_pos = torch.cos(position_ids.unsqueeze(-1) * self.inv_freq)
        sin_pos = torch.sin(position_ids.unsqueeze(-1) * self.inv_freq)
        
        # Efficient reshape for heads
        cos_pos = cos_pos.unsqueeze(1).repeat(1, H, 1, 1)
        sin_pos = sin_pos.unsqueeze(1).repeat(1, H, 1, 1)
        
        # Repeat for full head dimension
        cos_full = torch.cat([cos_pos, cos_pos], dim=-1)
        sin_full = torch.cat([sin_pos, sin_pos], dim=-1)
        
        # Normalize q and k along the head_dim to stabilize attention
        q, k = F.normalize(q, dim=-1), F.normalize(k, dim=-1)
        # Apply rotary embeddings
        q, k = self.apply_rotary_pos_emb(q, k, cos_full, sin_full)

        # Attention computation (bidirectional). Apply padding mask if provided.
        attn_mask = None
        if attention_mask is not None:
            # attention_mask: (B, T) with 1 for tokens to attend, 0 for PAD
            # We want to mask out PAD keys so queries cannot attend to them.
            key_pad = (~attention_mask.bool())  # True where PAD
            # Build additive mask with -inf on PAD keys, shape broadcastable to (B, H, T, T)
            additive = key_pad[:, None, None, :].to(dtype=torch.float32)
            additive = additive.masked_fill(additive > 0, float('-inf')).masked_fill(additive == 0, 0.0)
            attn_mask = additive
        y = F.scaled_dot_product_attention(q, k, v, attn_mask=attn_mask, is_causal=False)
        y = y.transpose(1, 2).contiguous().view(B, T, C)
        
        # Output projection
        y = self.c_proj(y)

        return y
