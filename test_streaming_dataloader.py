#!/usr/bin/env python3

import torch
import time
from data.dataloader_streaming import DataLoaderStreaming

def test_streaming_dataloader():
    """Test the streaming dataloader to ensure it works without pre-processing delays"""
    print("=== STREAMING DATALOADER TEST ===")
    
    # Test parameters
    B, T = 4, 1024
    process_rank = 0
    num_processes = 1
    split = 'train'
    
    print(f"Batch size: {B}, Sequence length: {T}")
    print("Starting streaming dataloader test...")
    
    # Measure initialization time
    start_time = time.time()
    
    try:
        # Initialize streaming dataloader
        dataloader = DataLoaderStreaming(
            B=B, 
            T=T, 
            process_rank=process_rank, 
            num_processes=num_processes, 
            split=split
        )
        
        init_time = time.time() - start_time
        print(f"✓ Dataloader initialized in {init_time:.2f} seconds")
        
        # Test batch generation
        print("\nTesting batch generation...")
        
        for batch_idx in range(3):
            batch_start = time.time()
            
            inputs, labels, attention_mask = dataloader.next_batch()
            
            batch_time = time.time() - batch_start
            
            # Validate batch shapes
            assert inputs.shape == (B, T), f"Expected inputs shape ({B}, {T}), got {inputs.shape}"
            assert labels.shape == (B, T), f"Expected labels shape ({B}, {T}), got {labels.shape}"
            assert attention_mask.shape == (B, T), f"Expected attention_mask shape ({B}, {T}), got {attention_mask.shape}"
            
            # Check for proper token distribution
            unique_input_tokens = torch.unique(inputs).numel()
            masked_positions = (inputs == dataloader.mask_token_id).sum().item()
            non_pad_tokens = (inputs != dataloader.pad_token_id).sum().item()
            
            print(f"Batch {batch_idx + 1}:")
            print(f"  ✓ Generated in {batch_time:.3f} seconds")
            print(f"  ✓ Unique tokens: {unique_input_tokens}")
            print(f"  ✓ Masked positions: {masked_positions}")
            print(f"  ✓ Non-padding tokens: {non_pad_tokens}")
            
            # Verify no all-padding sequences
            for i in range(B):
                non_pad_in_seq = (inputs[i] != dataloader.pad_token_id).sum().item()
                assert non_pad_in_seq > 2, f"Sequence {i} has only {non_pad_in_seq} non-padding tokens"
            
            print(f"  ✓ All sequences have sufficient content")
        
        print(f"\n✓ All tests passed!")
        print(f"✓ No pre-processing delay - ready to use immediately")
        
        # Test special tokens
        print(f"\nSpecial token IDs:")
        print(f"  CLS: {dataloader.cls_token_id}")
        print(f"  EOS: {dataloader.eos_token_id}")
        print(f"  PAD: {dataloader.pad_token_id}")
        print(f"  MASK: {dataloader.mask_token_id}")
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("Starting streaming dataloader test...")
    success = test_streaming_dataloader()
    
    if success:
        print("\n" + "="*50)
        print("STREAMING DATALOADER TEST COMPLETE")
        print("✓ Ready for training without pre-processing delays!")
        print("="*50)
    else:
        print("\n" + "="*50)
        print("STREAMING DATALOADER TEST FAILED")
        print("✗ Please check the errors above")
        print("="*50)
