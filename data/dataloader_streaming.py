import torch
import random
import threading
import time
from queue import Queue, Empty
from pathlib import Path
from transformers import AutoTokenizer
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor

# Import datasets with fallback
try:
    from datasets import load_dataset
except ImportError:
    print("Warning: datasets library not found. Please install with: pip install datasets")
    load_dataset = None


class DataLoaderStreaming:
    """Streaming data loader that tokenizes truly on-the-fly without pre-processing"""
    
    def __init__(self, B, T, process_rank, num_processes, split, target_tokens, tokenizer, dataset_paths=None):
        self.B = B
        self.T = T
        self.process_rank = process_rank
        self.num_processes = num_processes
        self.split = split
        self.target_tokens = target_tokens
        self.tokenizer = tokenizer
        
        # Multi-CLS configuration - get from tokenizer (set by model)
        self.use_multi_cls = getattr(tokenizer, 'use_multi_cls', False)
        self.n_cls_tokens = getattr(tokenizer, 'n_cls_tokens', 16) if self.use_multi_cls else 1
        
        # Cache special token IDs to avoid repeated lookups
        self.pad_token_id = self.tokenizer.pad_token_id
        self.cls_token_id = self.tokenizer.cls_token_id
        self.eos_token_id = self.tokenizer.eos_token_id
        self.mask_token_id = self.tokenizer.mask_token_id
        
        # Multi-CLS token IDs - get from tokenizer (set by model)
        if self.use_multi_cls:
            if hasattr(tokenizer, 'cls_token_ids') and isinstance(tokenizer.cls_token_ids, list):
                self.cls_token_ids = tokenizer.cls_token_ids
                print(f"DataLoader: Using model-provided CLS token IDs: {self.cls_token_ids}")
            else:
                # This shouldn't happen if model is initialized first
                print(f"Warning: DataLoader expected cls_token_ids from tokenizer but not found")
                vocab_size = len(tokenizer)
                cls_start_id = max(vocab_size + 100, 60000)  # Use same safe range as model
                self.cls_token_ids = list(range(cls_start_id, cls_start_id + self.n_cls_tokens))
                print(f"DataLoader: Created fallback CLS token IDs: {self.cls_token_ids}")
        else:
            self.cls_token_ids = []

        # Dataset paths from config
        if dataset_paths is None:
            dataset_paths = ["/s2_nfs/pile/OpenWebText2"]
        self.pile_subsets = dataset_paths
        
        # Get all Arrow files from the subsets
        self.data_files = []
        self.subset_files = {}
        
        for subset_path in self.pile_subsets:
            subset_name = Path(subset_path).name
            split_path = Path(subset_path) / split
            
            if split_path.exists():
                arrow_files = list(split_path.glob("*.arrow"))
                if arrow_files:
                    self.subset_files[subset_name] = arrow_files
                    self.data_files.extend(arrow_files)
                    if process_rank == 0:
                        print(f"Found {len(arrow_files)} .arrow files in {subset_name}/{split}")
                else:
                    if process_rank == 0:
                        print(f"Warning: No .arrow files found in {split_path}")
            else:
                if process_rank == 0:
                    print(f"Warning: Path {split_path} does not exist")
        
        assert len(self.data_files) > 0, f"No .arrow files found for split {split}"
        
        if process_rank == 0:
            print(f"Total: {len(self.data_files)} .arrow files for split {split}")
            print(f"Using streaming approach - no pre-processing")
        
        # Initialize random seed for consistent sampling across processes
        random.seed(1337 + process_rank)
        
        # Optimization: Larger token buffer and batch tokenization
        self.token_buffer_size = 50000  # Much larger buffer
        self.batch_tokenize_size = 100  # Tokenize multiple texts at once
        self.tokenization_workers = min(4, mp.cpu_count())  # Parallel tokenization
        
        # Pre-allocate larger buffers
        self.token_buffer = []
        self.token_position = 0
        self.text_batch_buffer = []  # Buffer for batching texts before tokenization
        
        # Pre-allocate tensors to avoid allocation overhead
        self.batch_buffer = torch.zeros((B, T), dtype=torch.long)
        self.labels_buffer = torch.full((B, T), -100, dtype=torch.long)
        self.mask_buffer = torch.zeros((B, T), dtype=torch.bool)
        
        # Streaming state
        self.current_file_idx = 0
        self.current_dataset = None
        self.current_iterator = None
        self.text_buffer = ""  # Buffer for partial text chunks
        
        # Initialize streaming
        self._load_next_file()
        
        # Skip to process-specific position
        self._skip_to_process_position()

    def _load_next_file(self):
        """Load next Arrow file as streaming dataset"""
        if load_dataset is None:
            raise ImportError("datasets library is required for loading files")
            
        file_path = self.data_files[self.current_file_idx]
        if self.process_rank == 0:
            print(f"Loading file: {file_path}")
        
        try:
            # Load as streaming dataset
            if file_path.suffix == '.arrow':
                self.current_dataset = load_dataset('arrow', data_files=str(file_path), streaming=True)['train']
            else:  # assume parquet or other format
                self.current_dataset = load_dataset('parquet', data_files=str(file_path), streaming=True)['train']
            
            # Create iterator
            self.current_iterator = iter(self.current_dataset)
            
            # Determine text column
            # For streaming datasets, we need to peek at first example to get column names
            try:
                first_example = next(self.current_iterator)
                self.text_column = None
                for col in ['text', 'content', 'article', 'document']:
                    if col in first_example:
                        self.text_column = col
                        break
                
                if self.text_column is None:
                    raise ValueError(f"No text column found in {file_path}. Available columns: {list(first_example.keys())}")
                
                # Put the first example back by recreating iterator and processing it
                self.current_iterator = iter(self.current_dataset)
                
            except StopIteration:
                # Empty file, move to next
                self._move_to_next_file()
                return
                
        except Exception as e:
            if self.process_rank == 0:
                print(f"Error loading {file_path}: {e}")
            self._move_to_next_file()

    def _move_to_next_file(self):
        """Move to next file in rotation"""
        self.current_file_idx = (self.current_file_idx + 1) % len(self.data_files)
        self.current_dataset = None
        self.current_iterator = None
        self.text_buffer = ""
        self.token_buffer = []
        self.token_position = 0
        self._load_next_file()

    def _skip_to_process_position(self):
        """Skip examples to reach process-specific starting position"""
        skip_count = self.B * self.T * self.process_rank
        if skip_count > 0 and self.process_rank == 0:
            print(f"Skipping {skip_count} tokens to reach process position...")
        
        # Skip by consuming tokens
        skipped = 0
        while skipped < skip_count:
            tokens = self._get_next_tokens(min(1000, skip_count - skipped))
            if not tokens:
                break
            skipped += len(tokens)

    def _get_next_example(self):
        """Get next example from current file, handling file transitions"""
        max_retries = len(self.data_files)  # Try all files if needed
        
        for _ in range(max_retries):
            try:
                if self.current_iterator is None:
                    self._load_next_file()
                    if self.current_iterator is None:
                        return None
                
                example = next(self.current_iterator)
                text = example.get(self.text_column, "")
                
                # Filter out very short texts
                if text and len(text.strip()) > 100:
                    return text.strip()
                    
            except StopIteration:
                # Current file exhausted, move to next
                self._move_to_next_file()
            except Exception as e:
                if self.process_rank == 0:
                    print(f"Error reading example: {e}")
                self._move_to_next_file()
        
        return None

    def _get_next_tokens(self, num_tokens):
        """Get next num_tokens tokens with optimized batch tokenization"""
        tokens = []
        
        while len(tokens) < num_tokens:
            # Use existing token buffer first
            if self.token_position < len(self.token_buffer):
                available = len(self.token_buffer) - self.token_position
                take = min(num_tokens - len(tokens), available)
                tokens.extend(self.token_buffer[self.token_position:self.token_position + take])
                self.token_position += take
                continue
            
            # Need more tokens - batch tokenize multiple texts
            self._refill_token_buffer()
            
            if not self.token_buffer:
                break
        
        return tokens

    def _refill_token_buffer(self):
        """Refill token buffer by batch tokenizing multiple texts"""
        # Collect batch of texts
        texts_to_tokenize = []
        while len(texts_to_tokenize) < self.batch_tokenize_size:
            text = self._get_next_example()
            if not text:
                break
            texts_to_tokenize.append(text)
        
        if not texts_to_tokenize:
            return
        
        # Batch tokenize all texts at once (much faster)
        try:
            # Use batch encoding for significant speedup
            encoded_batch = self.tokenizer(
                texts_to_tokenize,
                add_special_tokens=False,
                padding=False,
                truncation=False,
                return_attention_mask=False,
                return_token_type_ids=False
            )
            
            # Flatten all tokens into buffer
            new_tokens = []
            for input_ids in encoded_batch['input_ids']:
                new_tokens.extend(input_ids)
            
            self.token_buffer = new_tokens
            self.token_position = 0
            
        except Exception as e:
            if self.process_rank == 0:
                print(f"Batch tokenization failed: {e}, falling back to individual tokenization")
            # Fallback to individual tokenization
            new_tokens = []
            for text in texts_to_tokenize:
                tokens = self.tokenizer.encode(text, add_special_tokens=False)
                new_tokens.extend(tokens)
            self.token_buffer = new_tokens
            self.token_position = 0

    def _create_sequence_tokens(self):
        """Create tokens for one sequence with CLS and EOS"""
        if self.use_multi_cls:
            # Multi-CLS approach: reserve space for n_cls_tokens + EOS
            content_length = self.T - self.n_cls_tokens - 1  # Reserve space for multi-CLS + EOS
        else:
            # Standard approach: reserve space for CLS + EOS
            content_length = self.T - 2  # Reserve space for CLS and EOS
            
        content_tokens = self._get_next_tokens(content_length)
        
        if not content_tokens:
            # Fallback: create minimal sequence
            content_tokens = self.tokenizer.encode(".", add_special_tokens=False) * (content_length // 2)
            content_tokens = content_tokens[:content_length]
        
        # Pad if necessary
        while len(content_tokens) < content_length:
            content_tokens.extend(content_tokens[:min(len(content_tokens), content_length - len(content_tokens))])
        
        content_tokens = content_tokens[:content_length]
        
        # Build full sequence based on strategy
        if self.use_multi_cls:
            # Multi-CLS approach: [CLS1, CLS2, ..., CLS16] + content + [EOS] + padding
            # Ensure cls_token_ids is a list and has enough elements
            if not isinstance(self.cls_token_ids, list) or len(self.cls_token_ids) < self.n_cls_tokens:
                raise ValueError(f"cls_token_ids must be a list with at least {self.n_cls_tokens} elements, got: {self.cls_token_ids}")
            sequence = self.cls_token_ids[:self.n_cls_tokens] + content_tokens + [self.eos_token_id]
        else:
            # Standard approach: [CLS] + content + [EOS] + padding
            sequence = [self.cls_token_id] + content_tokens + [self.eos_token_id]
        
        # Pad to full length
        while len(sequence) < self.T:
            sequence.append(self.pad_token_id)
        
        return sequence[:self.T]

    def next_batch(self):
        """Return inputs, MLM labels, and attention mask using streaming tokenization"""
        B, T = self.B, self.T
        
        # Reuse pre-allocated buffers
        buf = self.batch_buffer
        buf.fill_(self.pad_token_id)

        # Generate sequences on-the-fly
        for i in range(B):
            sequence_tokens = self._create_sequence_tokens()
            buf[i] = torch.tensor(sequence_tokens, dtype=torch.long)

        # Create attention mask from ORIGINAL buffer BEFORE masking
        attention_mask = ((buf != self.pad_token_id).to(torch.long))

        # MLM masking
        inputs = buf.clone()
        labels = torch.full_like(buf, -100)
        mask_prob = 0.15
        rand = torch.rand(buf.shape)
        
        # Create mask excluding special tokens
        mask = (rand < mask_prob) & (buf != self.pad_token_id) & (buf != self.eos_token_id)
        
        # Exclude CLS tokens from masking
        if self.use_multi_cls:
            # Exclude all multi-CLS tokens
            for cls_id in self.cls_token_ids:
                mask = mask & (buf != cls_id)
        else:
            # Exclude single CLS token
            mask = mask & (buf != self.cls_token_id)
        
        # Ensure at least one token is masked per sequence
        for i in range(B):
            if not mask[i].any():
                # Find candidates (non-special tokens)
                candidates = (buf[i] != self.pad_token_id) & (buf[i] != self.eos_token_id)
                if self.use_multi_cls:
                    for cls_id in self.cls_token_ids:
                        candidates = candidates & (buf[i] != cls_id)
                else:
                    candidates = candidates & (buf[i] != self.cls_token_id)
                    
                if candidates.any():
                    idxs = torch.nonzero(candidates, as_tuple=False).squeeze(-1)
                    pick = idxs[torch.randint(0, idxs.numel(), (1,)).item()]
                    mask[i, pick] = True
        
        # Set labels for masked positions
        labels[mask] = buf[mask]
        inputs[mask] = self.mask_token_id

        # Ensure special tokens are ignored in loss
        labels[buf == self.pad_token_id] = -100
        labels[buf == self.eos_token_id] = -100
        if self.use_multi_cls:
            for cls_id in self.cls_token_ids:
                labels[buf == cls_id] = -100
        else:
            labels[buf == self.cls_token_id] = -100

        return inputs, labels, attention_mask
