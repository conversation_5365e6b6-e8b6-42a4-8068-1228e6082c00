import torch
import random
import threading
from queue import Queue
from pathlib import Path
from transformers import AutoTokenizer

# Import datasets with fallback
try:
    from datasets import load_dataset
except ImportError:
    print("Warning: datasets library not found. Please install with: pip install datasets")
    load_dataset = None


class DataLoaderLite:
    """Optimized data loader with pre-tokenization and background loading"""
    
    def __init__(self, B, T, process_rank, num_processes, split, target_tokens=20_000_000_000, tokenizer=None, dataset_paths=None):
        self.B = B
        self.T = T
        self.process_rank = process_rank
        self.num_processes = num_processes
        self.target_tokens = target_tokens
        assert split in {'train', 'test'}

        # Use provided tokenizer or load from default path
        if tokenizer is None:
            tokenizer_path = "/home/<USER>/.cache/huggingface/hub/models--BAAI--bge-m3/snapshots/5617a9f61b028005a4858fdac845db406aefb181"
            self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            if self.tokenizer.cls_token is None:
                self.tokenizer.add_special_tokens({'cls_token': '[CLS]'})
            if self.tokenizer.mask_token is None:
                self.tokenizer.add_special_tokens({'mask_token': '[MASK]'})
            self.tokenizer.model_max_length = 1000000
        else:
            self.tokenizer = tokenizer

        # Cache special token IDs to avoid repeated lookups
        self.pad_token_id = self.tokenizer.pad_token_id
        self.cls_token_id = self.tokenizer.cls_token_id
        self.eos_token_id = self.tokenizer.eos_token_id
        self.mask_token_id = self.tokenizer.mask_token_id

        # Dataset paths from config
        if dataset_paths is None:
            dataset_paths = ["/s2_nfs/pile/OpenWebText2"]
        self.pile_subsets = dataset_paths
        
        # Get all Arrow files from the subsets
        self.data_files = []
        self.subset_files = {}
        
        for subset_path in self.pile_subsets:
            subset_name = Path(subset_path).name
            split_path = Path(subset_path) / split
            
            if split_path.exists():
                arrow_files = list(split_path.glob("*.arrow"))
                if arrow_files:
                    self.subset_files[subset_name] = arrow_files
                    self.data_files.extend(arrow_files)
                    if process_rank == 0:
                        print(f"Found {len(arrow_files)} .arrow files in {subset_name}/{split}")
                else:
                    if process_rank == 0:
                        print(f"Warning: No .arrow files found in {split_path}")
            else:
                if process_rank == 0:
                    print(f"Warning: Path {split_path} does not exist")
        
        assert len(self.data_files) > 0, f"No .arrow files found for split {split}"
        
        if process_rank == 0:
            print(f"Total: {len(self.data_files)} .arrow files for split {split}")
            print(f"Using OpenWebText only - no sampling applied")
        
        # Initialize random seed for consistent sampling across processes
        random.seed(1337 + process_rank)
        
        # Pre-allocate tensors to avoid allocation overhead
        self.batch_buffer = torch.zeros((B, T), dtype=torch.long)
        self.labels_buffer = torch.full((B, T), -100, dtype=torch.long)
        self.mask_buffer = torch.zeros((B, T), dtype=torch.bool)
        
        # Background loading setup
        self.token_queue = Queue(maxsize=50)  # Buffer 50 batches worth of tokens
        self.loading_thread = None
        self.stop_loading = threading.Event()
        
        # Start with first file
        self.current_file_idx = 0
        self.current_shard_data = None
        self.current_position = 0
        
        # Start background loading
        self.start_background_loading()
        
        # Skip to process-specific position
        self.current_position = B * T * process_rank

    def start_background_loading(self):
        """Start background thread for loading and tokenizing data"""
        self.loading_thread = threading.Thread(target=self._background_loader, daemon=True)
        self.loading_thread.start()

    def _background_loader(self):
        """Background thread that loads and tokenizes data"""
        try:
            while not self.stop_loading.is_set():
                # Load current file if needed
                if self.current_shard_data is None:
                    self.load_current_file()
                
                # Get tokens for one batch worth of data
                batch_tokens = []
                for _ in range(self.B):
                    tokens = self.get_tokens_for_sequence()
                    if tokens:
                        batch_tokens.append(tokens)
                
                if batch_tokens:
                    # Put tokenized batch in queue (blocks if queue is full)
                    if not self.stop_loading.is_set():
                        self.token_queue.put(batch_tokens, timeout=1.0)
                else:
                    # No more data, move to next file
                    self.current_file_idx = (self.current_file_idx + 1) % len(self.data_files)
                    self.current_shard_data = None
                    
        except Exception as e:
            if self.process_rank == 0:
                print(f"Background loader error: {e}")

    def get_tokens_for_sequence(self):
        """Get tokens for one sequence, handling file transitions"""
        if self.current_shard_data is None:
            return None
            
        need = self.T - 2  # Leave room for CLS and EOS
        
        # Get tokens from current text buffer
        if hasattr(self, 'current_tokens') and self.token_position < len(self.current_tokens):
            available = len(self.current_tokens) - self.token_position
            take = min(need, available)
            tokens = self.current_tokens[self.token_position:self.token_position + take]
            self.token_position += take
            
            if len(tokens) < need:
                # Need more tokens, get next text
                if self.text_index < len(self.raw_texts):
                    text = self.raw_texts[self.text_index]
                    new_tokens = self.tokenizer.encode(text, add_special_tokens=False)
                    self.current_tokens = tokens + new_tokens
                    self.token_position = len(tokens)
                    self.text_index += 1
                    
                    # Try again to get full sequence
                    return self.get_tokens_for_sequence()
            
            return tokens[:need] if len(tokens) >= need else tokens
        else:
            # Need to get next text
            if hasattr(self, 'text_index') and self.text_index < len(self.raw_texts):
                text = self.raw_texts[self.text_index]
                self.current_tokens = self.tokenizer.encode(text, add_special_tokens=False)
                self.text_index += 1
                self.token_position = 0
                return self.get_tokens_for_sequence()
            else:
                # Need to load next file
                return None

    def load_current_file(self):
        """Load current Arrow/Parquet file using datasets library"""
        if load_dataset is None:
            raise ImportError("datasets library is required for loading files")
            
        file_path = self.data_files[self.current_file_idx]
        if self.process_rank == 0:
            print(f"Loading file: {file_path}")
        
        try:
            # Try loading as arrow first, then parquet
            if file_path.suffix == '.arrow':
                dataset_obj = load_dataset('arrow', data_files=str(file_path))['train']
            else:  # assume parquet or other format
                dataset_obj = load_dataset('parquet', data_files=str(file_path))['train']
            
            if self.process_rank == 0:
                print(f"Loaded dataset with {len(dataset_obj)} examples")
            
            # Extract text column (try different possible column names)
            text_column = None
            for col in ['text', 'content', 'article', 'document']:
                if col in dataset_obj.column_names:
                    text_column = col
                    break
            
            if text_column is None:
                raise ValueError(f"No text column found in {file_path}. Available columns: {dataset_obj.column_names}")
            
            # Filter and extract texts with chunking
            sampled_texts = []
            for example in dataset_obj:
                text = example[text_column]
                if not text or len(text.strip()) <= 100:
                    continue
                    
                # Chunk text into smaller pieces
                chunks = self.chunk_text(text.strip(), target_tokens=1022)
                sampled_texts.extend(chunks)
            
            # Store raw texts for tokenization
            self.raw_texts = sampled_texts
            self.text_index = 0
            self.current_tokens = []
            self.token_position = 0
            
            self.current_shard_data = True
            
            if self.process_rank == 0:
                print(f"Loaded {len(sampled_texts)} text chunks")
                
        except Exception as e:
            if self.process_rank == 0:
                print(f"Error loading {file_path}: {e}")
            
            # Skip to next file on error
            self.current_file_idx = (self.current_file_idx + 1) % len(self.data_files)
            if self.current_file_idx == 0:  # Wrapped around
                raise RuntimeError("All files failed to load")
            self.load_current_file()
            return
    
    def chunk_text(self, text, target_tokens=800, overlap_tokens=50):
        """Chunk long text into smaller pieces suitable for 1024 context length"""
        # Tokenize the full text with AutoTokenizer
        tokens = self.tokenizer.encode(text, add_special_tokens=False)
        
        # If text is short enough, return as single chunk
        if len(tokens) <= target_tokens:
            return [text]
        
        chunks = []
        start_idx = 0
        
        while start_idx < len(tokens):
            # Extract chunk of target size
            end_idx = min(start_idx + target_tokens, len(tokens))
            chunk_tokens = tokens[start_idx:end_idx]
            
            # Decode back to text - preserve all content, no truncation
            chunk_text = self.tokenizer.decode(chunk_tokens, skip_special_tokens=True)
            chunks.append(chunk_text.strip())
            
            # Move start position with overlap to ensure no text is lost
            if end_idx >= len(tokens):
                break
            start_idx = end_idx - overlap_tokens
        
        return chunks

    def next_batch(self):
        """Return inputs, MLM labels, and attention mask using pre-loaded tokens"""
        B, T = self.B, self.T
        
        # Get pre-tokenized batch from queue
        try:
            batch_tokens = self.token_queue.get(timeout=5.0)
        except:
            # Fallback: generate batch synchronously
            batch_tokens = []
            for _ in range(B):
                tokens = self.get_tokens_for_sequence()
                if tokens:
                    batch_tokens.append(tokens)
        
        # Reuse pre-allocated buffers
        buf = self.batch_buffer
        buf.fill_(self.pad_token_id)

        for i, tokens in enumerate(batch_tokens[:B]):
            if not tokens:
                continue
                
            # Handle case where we don't get enough tokens
            need = T - 2
            if len(tokens) < need:
                # Pad with available tokens or repeat if necessary
                while len(tokens) < need:
                    if len(tokens) == 0:
                        fallback_tokens = self.tokenizer.encode(".", add_special_tokens=False)
                        tokens.extend(fallback_tokens * ((need // len(fallback_tokens)) + 1))
                        break
                    repeat_count = min(len(tokens), need - len(tokens))
                    tokens.extend(tokens[:repeat_count])
                tokens = tokens[:need]
            
            # Create sequence with special tokens
            buf[i, 0] = self.cls_token_id
            
            # Place actual content tokens
            actual_tokens = min(len(tokens), need)
            if actual_tokens > 0:
                buf[i, 1:1+actual_tokens] = torch.tensor(tokens[:actual_tokens], dtype=torch.long)
            
            # Place EOS right after actual content tokens
            eos_position = 1 + actual_tokens
            if eos_position < T:
                buf[i, eos_position] = self.eos_token_id

        # Create attention mask from ORIGINAL buffer BEFORE masking
        attention_mask = ((buf != self.pad_token_id).to(torch.long))

        # Prepare MLM inputs and labels using pre-allocated buffers
        inputs = buf.clone()
        labels = self.labels_buffer
        labels.fill_(-100)

        # 30% mask probability (do not mask PAD, CLS, or EOS tokens)
        mask_prob = 0.3
        rand = torch.rand(buf.shape)
        mask = self.mask_buffer
        mask.copy_((rand < mask_prob) & (buf != self.pad_token_id) & 
                  (buf != self.cls_token_id) & (buf != self.eos_token_id))
        
        # Ensure at least one masked token per sequence
        for i in range(B):
            if not mask[i].any():
                candidates = (buf[i] != self.pad_token_id) & (buf[i] != self.cls_token_id) & (buf[i] != self.eos_token_id)
                if candidates.any():
                    idxs = torch.nonzero(candidates, as_tuple=False).squeeze(-1)
                    pick = idxs[torch.randint(0, idxs.numel(), (1,)).item()]
                    mask[i, pick] = True
        
        # Set labels for masked positions
        labels[mask] = buf[mask]
        inputs[mask] = self.mask_token_id

        # Ensure special tokens are ignored in loss (redundant but safe)
        labels[buf == self.pad_token_id] = -100
        labels[buf == self.cls_token_id] = -100
        labels[buf == self.eos_token_id] = -100

        return inputs, labels, attention_mask

    def __del__(self):
        """Clean up background thread"""
        if hasattr(self, 'stop_loading'):
            self.stop_loading.set()
        if hasattr(self, 'loading_thread') and self.loading_thread:
            self.loading_thread.join(timeout=1.0)
