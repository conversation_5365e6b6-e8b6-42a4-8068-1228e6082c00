import torch
import random
import threading
import time
import numpy as np
from queue import Queue, Empty
from pathlib import Path
from transformers import AutoTokenizer
import tiktoken

# Import datasets with fallback
try:
    from datasets import load_dataset
except ImportError:
    print("Warning: datasets library not found. Please install with: pip install datasets")
    load_dataset = None


def get_tokenizer_info(tokenizer_name):
    """
    Get tokenizer information including vocab_size and special token IDs.
    Supports both tiktoken encodings and custom tokenizer paths.
    """
    tokenizer_info = {
        'tokenizer': None,
        'vocab_size': None,
        'mask_token_id': None,
        'pad_token_id': None,
        'cls_token_id': None,
        'eos_token_id': None,
        'has_special_tokens': False
    }

    # Check if it's a tiktoken encoding name or custom path
    if tokenizer_name.startswith('/') or '\\' in tokenizer_name:
        # Custom tokenizer path - try to load it
        try:
            tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)

            tokenizer_info['tokenizer'] = tokenizer
            tokenizer_info['vocab_size'] = len(tokenizer.vocab) if hasattr(tokenizer, 'vocab') else tokenizer.vocab_size
            tokenizer_info['has_special_tokens'] = True

            # Get special token IDs
            tokenizer_info['mask_token_id'] = getattr(tokenizer, 'mask_token_id', None)
            tokenizer_info['pad_token_id'] = getattr(tokenizer, 'pad_token_id', None)
            tokenizer_info['cls_token_id'] = getattr(tokenizer, 'cls_token_id', None)
            tokenizer_info['eos_token_id'] = getattr(tokenizer, 'eos_token_id', None)

            # Some tokenizers might use different attribute names
            if tokenizer_info['cls_token_id'] is None:
                tokenizer_info['cls_token_id'] = getattr(tokenizer, 'bos_token_id', None)

            print(f"Loaded custom tokenizer from {tokenizer_name}")
            print(f"  Vocab size: {tokenizer_info['vocab_size']}")
            print(f"  Special tokens - MASK: {tokenizer_info['mask_token_id']}, PAD: {tokenizer_info['pad_token_id']}, CLS: {tokenizer_info['cls_token_id']}, EOS: {tokenizer_info['eos_token_id']}")

        except Exception as e:
            print(f"Warning: Could not load custom tokenizer from {tokenizer_name}: {e}")
            print("Falling back to tiktoken gpt2 encoding")
            tokenizer_name = "gpt2"  # Fallback

    # Handle tiktoken encodings (including fallback)
    if not tokenizer_info['tokenizer']:
        try:
            tokenizer = tiktoken.get_encoding(tokenizer_name)
            tokenizer_info['tokenizer'] = tokenizer
            tokenizer_info['vocab_size'] = tokenizer.n_vocab
            tokenizer_info['has_special_tokens'] = False

            # tiktoken encodings don't have built-in special tokens, so we add them
            base_vocab_size = tokenizer.n_vocab
            tokenizer_info['mask_token_id'] = base_vocab_size + 0
            tokenizer_info['pad_token_id'] = base_vocab_size + 1
            tokenizer_info['cls_token_id'] = base_vocab_size + 2
            tokenizer_info['eos_token_id'] = base_vocab_size + 3

            # Update vocab_size to include special tokens
            tokenizer_info['vocab_size'] = base_vocab_size + 4

            print(f"Loaded tiktoken encoding: {tokenizer_name}")
            print(f"  Base vocab size: {base_vocab_size}, Extended vocab size: {tokenizer_info['vocab_size']}")
            print(f"  Added special tokens - MASK: {tokenizer_info['mask_token_id']}, PAD: {tokenizer_info['pad_token_id']}, CLS: {tokenizer_info['cls_token_id']}, EOS: {tokenizer_info['eos_token_id']}")

        except Exception as e:
            raise ValueError(f"Could not load tokenizer '{tokenizer_name}': {e}")

    return tokenizer_info


class DataLoaderLite:
    """
    Unified data loader integrating principles from retrieval_mlm_configurable.py:
    - Supports both pre-tokenized .npy files and on-the-fly Arrow tokenization
    - Configurable tokenizer support (tiktoken + transformers)
    - Proper MLM masking with special tokens
    - Background loading with race condition fixes
    - Progress indicators and robust error handling
    """
    
    def __init__(self, B, T, process_rank, num_processes, split, 
                 tokenizer_name=None, dataset_paths=None, tokenize_on_the_fly=False,
                 mask_prob=0.3, target_tokens=20_000_000_000):
        self.B = B
        self.T = T
        self.process_rank = process_rank
        self.num_processes = num_processes
        self.target_tokens = target_tokens
        self.mask_prob = mask_prob
        self.tokenize_on_the_fly = tokenize_on_the_fly
        assert split in {'train', 'val', 'test'}
        self.split = split

        # Get tokenizer information
        if tokenizer_name is None:
            tokenizer_name = "/home/<USER>/.cache/huggingface/hub/models--BAAI--bge-m3/snapshots/5617a9f61b028005a4858fdac845db406aefb181"
        
        self.tokenizer_info = get_tokenizer_info(tokenizer_name)
        self.tokenizer = self.tokenizer_info['tokenizer']

        # Cache special token IDs for easy access
        self.mask_token_id = self.tokenizer_info['mask_token_id']
        self.pad_token_id = self.tokenizer_info['pad_token_id']
        self.cls_token_id = self.tokenizer_info['cls_token_id']
        self.eos_token_id = self.tokenizer_info['eos_token_id']

        # Initialize random seed for consistent sampling across processes
        random.seed(1337 + process_rank)
        
        # Pre-allocate tensors to avoid allocation overhead
        self.batch_buffer = torch.zeros((B, T), dtype=torch.long)
        self.labels_buffer = torch.full((B, T), -100, dtype=torch.long)
        self.mask_buffer = torch.zeros((B, T), dtype=torch.bool)
        
        # Background loading setup
        self.token_queue = Queue(maxsize=50)  # Buffer 50 batches worth of tokens
        self.loading_thread = None
        self.stop_loading = threading.Event()
        self.data_ready = threading.Event()  # Signal when initial data is loaded
        
        # Initialize based on mode
        if self.tokenize_on_the_fly:
            self._init_arrow_mode(dataset_paths)
        else:
            self._init_npy_mode(dataset_paths)
        
        # Signal that data is ready
        self.data_ready.set()
        
        # Start background loading AFTER initial data is loaded
        self.start_background_loading()
        
        # Skip to process-specific position
        self.current_position = B * T * process_rank

    def _init_npy_mode(self, dataset_paths):
        """Initialize for pre-tokenized .npy file loading"""
        if self.process_rank == 0:
            print("Initializing DataLoader in pre-tokenized (.npy) mode")

        # Default paths for .npy files
        if dataset_paths is None:
            if self.split == "train":
                dataset_paths = ["./data/fineweb10B/*.npy"]
            else:
                dataset_paths = ["./data/fineweb_val/*.npy"]
        
        # Get .npy files from patterns
        import glob
        self.data_files = []
        for pattern in dataset_paths:
            files = sorted(glob.glob(pattern))
            self.data_files.extend(files)
        
        assert len(self.data_files) > 0, f"No .npy files found for patterns {dataset_paths}"
        
        if self.process_rank == 0:
            print(f"Found {len(self.data_files)} .npy files for split {self.split}")

        # Start with first file
        self.current_file_idx = 0
        self.load_current_file()

    def _init_arrow_mode(self, dataset_paths):
        """Initialize for on-the-fly tokenization from Arrow files"""
        if self.process_rank == 0:
            print("Initializing DataLoader in on-the-fly tokenization (Arrow) mode")

        # Default paths for Arrow files
        if dataset_paths is None:
            dataset_paths = ["/s2_nfs/pile/OpenWebText2"]
        
        # Map split names
        arrow_split = "train" if self.split == "train" else "test"
        
        # Get all Arrow files from the specified paths
        self.data_files = []
        self.subset_files = {}
        
        for subset_path in dataset_paths:
            subset_name = Path(subset_path).name
            split_path = Path(subset_path) / arrow_split
            
            if split_path.exists():
                arrow_files = list(split_path.glob("*.arrow"))
                if arrow_files:
                    self.subset_files[subset_name] = arrow_files
                    self.data_files.extend(arrow_files)
                    if self.process_rank == 0:
                        print(f"Found {len(arrow_files)} .arrow files in {subset_name}/{arrow_split}")
                else:
                    if self.process_rank == 0:
                        print(f"Warning: No .arrow files found in {split_path}")
            else:
                if self.process_rank == 0:
                    print(f"Warning: Path {split_path} does not exist")
        
        assert len(self.data_files) > 0, f"No .arrow files found for split {arrow_split}"
        
        if self.process_rank == 0:
            print(f"Total: {len(self.data_files)} .arrow files for split {arrow_split}")

        # Start with first file
        self.current_file_idx = 0
        self.current_shard_data = None
        self.current_position = 0
        self.load_current_file()

    def start_background_loading(self):
        """Start background thread for loading and tokenizing data"""
        self.loading_thread = threading.Thread(target=self._background_loader, daemon=True)
        self.loading_thread.start()

    def _background_loader(self):
        """Background thread that loads and tokenizes data"""
        try:
            while not self.stop_loading.is_set():
                # Generate batch tokens based on mode
                if self.tokenize_on_the_fly:
                    batch_tokens = self._generate_batch_tokens_arrow()
                else:
                    batch_tokens = self._generate_batch_tokens_npy()
                
                if batch_tokens:
                    # Put tokenized batch in queue (blocks if queue is full)
                    if not self.stop_loading.is_set():
                        try:
                            self.token_queue.put(batch_tokens, timeout=1.0)
                        except:
                            # Queue full or stopping, continue
                            pass
                else:
                    # No more data, move to next file
                    self.current_file_idx = (self.current_file_idx + 1) % len(self.data_files)
                    self.load_current_file()
                    
        except Exception as e:
            if self.process_rank == 0:
                print(f"Background loader error: {e}")

    def _generate_batch_tokens_npy(self):
        """Generate batch tokens from .npy files"""
        batch_tokens = []
        for _ in range(self.B):
            # We need T-2 tokens from dataset (leaving room for [CLS] and [EOS])
            need = self.T - 2
            if self.current_position + need > len(self.tokens):
                # Move to next file
                self.current_file_idx = (self.current_file_idx + 1) % len(self.data_files)
                self.load_current_file()
                self.current_position = self.B * self.T * self.process_rank

            # Extract sequence
            seq = self.tokens[self.current_position:self.current_position + need]
            batch_tokens.append(seq.tolist())
            self.current_position += need
        
        return batch_tokens

    def _generate_batch_tokens_arrow(self):
        """Generate batch tokens from Arrow files with on-the-fly tokenization"""
        batch_tokens = []
        for _ in range(self.B):
            tokens = self.get_tokens_for_sequence()
            if tokens:
                batch_tokens.append(tokens)
        
        return batch_tokens if len(batch_tokens) == self.B else None

    def get_tokens_for_sequence(self):
        """Get tokens for one sequence, handling file transitions (Arrow mode)"""
        if not hasattr(self, 'raw_texts') or not self.raw_texts:
            return None
            
        need = self.T - 2  # Leave room for CLS and EOS
        
        # Get tokens from current text buffer
        if hasattr(self, 'current_tokens') and self.token_position < len(self.current_tokens):
            available = len(self.current_tokens) - self.token_position
            take = min(need, available)
            tokens = self.current_tokens[self.token_position:self.token_position + take]
            self.token_position += take
            
            if len(tokens) < need:
                # Need more tokens, get next text
                if self.text_index < len(self.raw_texts):
                    text = self.raw_texts[self.text_index]
                    # Handle both tiktoken and transformers tokenizers
                    if hasattr(self.tokenizer, 'encode_ordinary'):
                        new_tokens = self.tokenizer.encode(text)
                    else:
                        new_tokens = self.tokenizer.encode(text, add_special_tokens=False)
                    self.current_tokens = tokens + new_tokens
                    self.token_position = len(tokens)
                    self.text_index += 1
                    
                    # Try again to get full sequence
                    return self.get_tokens_for_sequence()
            
            return tokens[:need] if len(tokens) >= need else tokens
        else:
            # Need to get next text
            if hasattr(self, 'text_index') and self.text_index < len(self.raw_texts):
                text = self.raw_texts[self.text_index]
                if hasattr(self.tokenizer, 'encode_ordinary'):
                    self.current_tokens = self.tokenizer.encode(text)
                else:
                    self.current_tokens = self.tokenizer.encode(text, add_special_tokens=False)
                self.text_index += 1
                self.token_position = 0
                return self.get_tokens_for_sequence()
            else:
                # Need to load next file
                return None

    def load_current_file(self):
        """Load current file - either .npy or Arrow depending on mode"""
        if self.tokenize_on_the_fly:
            self._load_arrow_file()
        else:
            self._load_npy_file()

    def _load_npy_file(self):
        """Load current .npy file"""
        file_path = self.data_files[self.current_file_idx]
        if self.process_rank == 0:
            print(f"Loading .npy file: {file_path}")

        # Load the tokenized data
        self.tokens = np.load(file_path)
        self.tokens = self.tokens.astype(np.int32)

        if self.process_rank == 0:
            print(f"Loaded {len(self.tokens):,} tokens from {file_path}")

    def _load_arrow_file(self):
        """Load current Arrow file using datasets library"""
        if load_dataset is None:
            raise ImportError("datasets library is required for loading Arrow files")
            
        file_path = self.data_files[self.current_file_idx]
        if self.process_rank == 0:
            print(f"Loading Arrow file: {file_path}")
        
        try:
            # Try loading as arrow first, then parquet
            if file_path.suffix == '.arrow':
                dataset_obj = load_dataset('arrow', data_files=str(file_path))['train']
            else:
                dataset_obj = load_dataset('parquet', data_files=str(file_path))['train']
            
            if self.process_rank == 0:
                print(f"Loaded dataset with {len(dataset_obj)} examples")
            
            # Check if data is already tokenized or raw text
            if 'token_ids' in dataset_obj.column_names:
                # Pre-tokenized data
                if self.process_rank == 0:
                    print("Found pre-tokenized data with 'token_ids' column")
                
                all_tokens = []
                for example in dataset_obj:
                    tokens = example['token_ids']
                    if isinstance(tokens, list) and len(tokens) > 0:
                        all_tokens.extend(tokens)
                
                self.raw_texts = []
                self.current_tokens = all_tokens
                self.text_index = 0
                self.token_position = 0
                return
            
            # Raw text data - extract text column
            text_column = None
            for col in ['text', 'content', 'article', 'document']:
                if col in dataset_obj.column_names:
                    text_column = col
                    break
            
            if text_column is None:
                raise ValueError(f"No text column found in {file_path}. Available columns: {dataset_obj.column_names}")
            
            # Filter and extract texts with chunking - ADD PROGRESS INDICATOR
            sampled_texts = []
            total_examples = len(dataset_obj)
            processed = 0
            
            for example in dataset_obj:
                text = example[text_column]
                if not text or len(text.strip()) <= 100:
                    continue
                    
                # Chunk text into smaller pieces
                chunks = self.chunk_text(text.strip(), target_tokens=1022)
                sampled_texts.extend(chunks)
                
                # Progress indicator every 10k examples
                processed += 1
                if processed % 10000 == 0 and self.process_rank == 0:
                    print(f"Processed {processed}/{total_examples} examples ({processed/total_examples*100:.1f}%)")
            
            # Store raw texts for tokenization
            self.raw_texts = sampled_texts
            self.text_index = 0
            self.current_tokens = []
            self.token_position = 0
            
            if self.process_rank == 0:
                print(f"Loaded {len(sampled_texts)} text chunks")
                
        except Exception as e:
            if self.process_rank == 0:
                print(f"Error loading {file_path}: {e}")
            
            # Skip to next file on error
            self.current_file_idx = (self.current_file_idx + 1) % len(self.data_files)
            if self.current_file_idx == 0:  # Wrapped around
                raise RuntimeError("All files failed to load")
            self.load_current_file()
            return
    
    def chunk_text(self, text, target_tokens=800, overlap_tokens=50):
        """Chunk long text into smaller pieces suitable for 1024 context length"""
        # Tokenize the full text
        if hasattr(self.tokenizer, 'encode_ordinary'):
            tokens = self.tokenizer.encode(text)
        else:
            tokens = self.tokenizer.encode(text, add_special_tokens=False)
        
        # If text is short enough, return as single chunk
        if len(tokens) <= target_tokens:
            return [text]
        
        chunks = []
        start_idx = 0
        
        while start_idx < len(tokens):
            # Extract chunk of target size
            end_idx = min(start_idx + target_tokens, len(tokens))
            chunk_tokens = tokens[start_idx:end_idx]
            
            # Decode back to text
            if hasattr(self.tokenizer, 'decode'):
                chunk_text = self.tokenizer.decode(chunk_tokens)
            else:
                # For transformers tokenizers
                chunk_text = self.tokenizer.decode(chunk_tokens, skip_special_tokens=True)
            
            chunks.append(chunk_text.strip())
            
            # Move start position with overlap to ensure no text is lost
            if end_idx >= len(tokens):
                break
            start_idx = end_idx - overlap_tokens
        
        return chunks

    def next_batch(self):
        """Return inputs, MLM labels, and attention mask using pre-loaded tokens"""
        B, T = self.B, self.T
        
        # Wait for initial data to be ready
        if not self.data_ready.wait(timeout=30):
            raise RuntimeError("Timeout waiting for initial data to load")
        
        # Get pre-tokenized batch from queue with better timeout handling
        batch_tokens = None
        max_retries = 3
        
        for retry in range(max_retries):
            try:
                batch_tokens = self.token_queue.get(timeout=10.0)
                break
            except Empty:
                if self.process_rank == 0:
                    print(f"Queue empty, retry {retry + 1}/{max_retries}...")
                time.sleep(1.0)
        
        # Final fallback: generate batch synchronously
        if batch_tokens is None:
            if self.process_rank == 0:
                print("Generating batch synchronously as fallback...")
            if self.tokenize_on_the_fly:
                batch_tokens = self._generate_batch_tokens_arrow()
            else:
                batch_tokens = self._generate_batch_tokens_npy()
        
        # Create batch buffer
        buf = self.batch_buffer
        buf.fill_(self.pad_token_id)

        for i, tokens in enumerate(batch_tokens[:B]):
            if not tokens:
                continue
                
            # Handle case where we don't get enough tokens
            need = T - 2
            if len(tokens) < need:
                # Pad with available tokens or repeat if necessary
                while len(tokens) < need:
                    if len(tokens) == 0:
                        # Emergency fallback
                        fallback_tokens = [1] * need  # Use token ID 1 as fallback
                        tokens.extend(fallback_tokens)
                        break
                    repeat_count = min(len(tokens), need - len(tokens))
                    tokens.extend(tokens[:repeat_count])
                tokens = tokens[:need]
            
            # Create sequence with special tokens: [CLS] + content + [EOS] + [PAD]...
            buf[i, 0] = self.cls_token_id
            
            # Place actual content tokens
            actual_tokens = min(len(tokens), need)
            if actual_tokens > 0:
                buf[i, 1:1+actual_tokens] = torch.tensor(tokens[:actual_tokens], dtype=torch.long)
            
            # Place EOS right after actual content tokens
            eos_position = 1 + actual_tokens
            if eos_position < T:
                buf[i, eos_position] = self.eos_token_id

        # Apply MLM masking
        return self._apply_mlm_masking(buf)

    def _apply_mlm_masking(self, buf):
        """Apply MLM masking to the input buffer"""
        B, T = buf.shape

        # Create attention mask from ORIGINAL buffer BEFORE masking
        attention_mask = ((buf != self.pad_token_id).to(torch.long))

        # Prepare MLM inputs and labels using pre-allocated buffers
        inputs = buf.clone()
        labels = self.labels_buffer
        labels.fill_(-100)

        # Apply mask probability (do not mask PAD, CLS, or EOS tokens)
        rand = torch.rand(buf.shape)
        mask = self.mask_buffer
        mask.copy_((rand < self.mask_prob) & (buf != self.pad_token_id) & 
                  (buf != self.cls_token_id) & (buf != self.eos_token_id))
        
        # Ensure at least one masked token per sequence
        for i in range(B):
            if not mask[i].any():
                candidates = (buf[i] != self.pad_token_id) & (buf[i] != self.cls_token_id) & (buf[i] != self.eos_token_id)
                if candidates.any():
                    idxs = torch.nonzero(candidates, as_tuple=False).squeeze(-1)
                    pick = idxs[torch.randint(0, idxs.numel(), (1,)).item()]
                    mask[i, pick] = True
        
        # Set labels for masked positions
        labels[mask] = buf[mask]
        inputs[mask] = self.mask_token_id

        # Ensure special tokens are ignored in loss
        labels[buf == self.pad_token_id] = -100
        labels[buf == self.cls_token_id] = -100
        labels[buf == self.eos_token_id] = -100

        return inputs, labels, attention_mask

    def __del__(self):
        """Clean up background thread"""
        if hasattr(self, 'stop_loading'):
            self.stop_loading.set()
        if hasattr(self, 'loading_thread') and self.loading_thread:
            self.loading_thread.join(timeout=1.0)
