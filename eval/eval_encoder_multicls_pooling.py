import os
import sys
import math
import argparse
import json
from typing import List, Dict, <PERSON><PERSON>

import torch
import torch.nn.functional as F

# External deps expected:
#   pip install beir transformers sentencepiece
try:
    from beir import util
    from beir.datasets.data_loader import GenericDataLoader
except Exception as e:
    raise RuntimeError(f"BEIR base import failed: {e}. Try: pip install beir") from e

_eval_import_error = None
try:
    from beir.retrieval.evaluation import EvaluateR<PERSON>rieval as Evaluate
except Exception as e:
    Evaluate = None
    _eval_import_error = e

try:
    from transformers import GPT2TokenizerFast, AutoTokenizer
except Exception:
    GPT2TokenizerFast = None
    AutoTokenizer = None

# Import your model
sys.path.append(os.path.dirname(os.path.dirname(__file__)))  # Add parent directory to path
from models.masked_language_model import MaskedLanguageModel
from models.config import load_model_config_from_yaml


def build_tokenizer(model_vocab_size: int, tokenizer_path: str = None):
    if AutoTokenizer is None or GPT2TokenizerFast is None:
        raise RuntimeError("transformers not installed. Please: pip install transformers sentencepiece")

    # Align special tokens to model's assumptions:
    # [MASK] -> id = vocab_size, [PAD] -> vocab_size+1, [CLS1-16] -> vocab_size+2 to vocab_size+17
    cls_tokens = [f"[CLS{i+1}]" for i in range(16)]
    
    # Use provided tokenizer path or default to GPT2
    if tokenizer_path:
        print(f"[Info] Loading tokenizer from: {tokenizer_path}")
        tok = AutoTokenizer.from_pretrained(tokenizer_path)
        special_tokens = {"additional_special_tokens": [cls_tokens}
    else:
        print("[Info] Using default GPT2 tokenizer")
        tok = GPT2TokenizerFast.from_pretrained("gpt2")
        special_tokens = {"additional_special_tokens": ["[MASK]", "[PAD]"] + cls_tokens}
        
    tok.add_special_tokens(special_tokens)
    tok.pad_token = "[PAD]"

    # Get special token IDs
    mask_id = tok.convert_tokens_to_ids("[MASK]")
    pad_id = tok.convert_tokens_to_ids("[PAD]")
    cls_ids = [tok.convert_tokens_to_ids(f"[CLS{i+1}]") for i in range(16)]

    # Verify alignment
    print(f"[Info] Tokenizer vocab size: {len(tok)} (base: {model_vocab_size})")
    print(f"[Info] Special tokens - MASK: {mask_id}, PAD: {pad_id}")
    print(f"[Info] CLS tokens: {cls_ids[:3]}...{cls_ids[-3:]} (showing first 3 and last 3)")

    # Clamp tokenizer vocab to model's effective range
    effective_vocab_size = model_vocab_size + 2 + 16  # MASK + PAD + 16*CLS
    if len(tok) > effective_vocab_size:
        print(f"[Warn] Tokenizer vocab ({len(tok)}) > model vocab ({effective_vocab_size}). Will clamp token IDs.")

    return tok, mask_id, pad_id, cls_ids, effective_vocab_size


def batch_encode_with_multicls(tokenizer, texts: List[str], pad_id: int, max_len: int, device: torch.device, effective_vocab_size: int, cls_ids: List[int], n_cls_tokens: int = 16):
    # Encode without adding special tokens, prepend 16 CLS tokens, then pad
    enc = tokenizer(texts, add_special_tokens=False, padding=False, truncation=True, max_length=max_len-n_cls_tokens, return_attention_mask=False)
    input_ids = []
    
    # Use the actual 16 different CLS token IDs
    cls_token_ids = cls_ids
    
    for ids in enc["input_ids"]:
        # Clamp token IDs to model's vocabulary range
        ids = [min(token_id, effective_vocab_size - n_cls_tokens - 1) for token_id in ids]
        # Prepend 16 CLS tokens
        ids = cls_token_ids + ids
        # pad to max_len
        if len(ids) < max_len:
            ids = ids + [pad_id] * (max_len - len(ids))
        else:
            ids = ids[:max_len]
        input_ids.append(ids)
    
    input_ids = torch.tensor(input_ids, dtype=torch.long, device=device)
    attention_mask = (input_ids != pad_id).long()
    return input_ids, attention_mask


def get_cls_embeddings(model: MaskedLanguageModel, input_ids: torch.Tensor, attention_mask: torch.Tensor, normalize: bool = True, pooling: str = "cls_avg", debug: bool = False):
    # Handle embeddings: regular tokens use wte, CLS tokens use cls_embeddings
    B, T = input_ids.size()
    x = torch.zeros(B, T, model.config.n_embd, device=input_ids.device, dtype=model.transformer.wte.weight.dtype)
    
    # Regular tokens (including MASK, PAD)
    regular_mask = input_ids < (model.config.vocab_size + 2)  # vocab + MASK + PAD
    if regular_mask.any():
        x[regular_mask] = model.transformer.wte(input_ids[regular_mask])
    
    # CLS tokens
    cls_token_ids = list(range(model.config.vocab_size + 2, model.config.vocab_size + 2 + model.config.n_cls_tokens))
    for i, cls_id in enumerate(cls_token_ids):
        cls_mask = (input_ids == cls_id)
        if cls_mask.any():
            x[cls_mask] = model.cls_embeddings[i]
    
    if debug:
        cls_after_embed = x[:, :16, :]  # CLS tokens after embedding
        print(f"[Debug] After embedding - ||CLS1 - CLS2||: {torch.norm(cls_after_embed[0,0] - cls_after_embed[0,1]).item():.6f}")
    
    # Special handling for cls_concat_pre_proj pooling
    if pooling == "cls_concat_pre_proj":
        # Process all blocks except the last one
        for block in model.transformer.h[:-1]:
            x = block(x, attention_mask=attention_mask)
        
        # For the last block, we need to extract representations before c_proj
        if len(model.transformer.h) > 0:
            final_block = model.transformer.h[-1]
            
            # Apply layer norm and attention computation, but extract before c_proj
            x_normed = final_block.ln_1(x)
            
            # Get the attention output before c_proj (manually compute attention)
            attn = final_block.attn
            B, T, C = x_normed.size()
            H = attn.n_head
            head_dim = C // H
            
            # Time-mixing logic (same as in the attention module)
            x_left = torch.zeros_like(x_normed)
            x_left[:, 1:, :] = x_normed[:, :-1, :]
            x_right = torch.zeros_like(x_normed)
            x_right[:, :-1, :] = x_normed[:, 1:, :]
            if attention_mask is not None:
                m = attention_mask.to(dtype=x_normed.dtype).unsqueeze(-1)
                left_mask = torch.zeros_like(m)
                left_mask[:, 1:, :] = m[:, :-1, :]
                right_mask = torch.zeros_like(m)
                right_mask[:, :-1, :] = m[:, 1:, :]
                numer = x_left * left_mask + x_right * right_mask
                denom = left_mask + right_mask
                x_neighbors = torch.where(denom > 0, numer / torch.clamp(denom, min=1.0), x_normed)
            else:
                x_neighbors = 0.5 * (x_left + x_right)
            xx = x_neighbors - x_normed
            x_base = x_normed + xx * attn.time_maa_x
            lora_pre_bmm = torch.tanh(x_base @ attn.time_maa_w1).view(B * T, 3, -1).transpose(0, 1)
            lora_components = torch.bmm(lora_pre_bmm, attn.time_maa_w2).view(3, B, T, C)
            lora_q, lora_k, lora_v = lora_components.unbind(dim=0)
            
            xq_intermediate = x_normed + xx * (attn.time_maa_q + lora_q)
            xk_intermediate = x_normed + xx * (attn.time_maa_k + lora_k)
            xv_intermediate = x_normed + xx * (attn.time_maa_v + lora_v)

            # Projections
            q = attn.query(xq_intermediate).view(B, T, H, head_dim).transpose(1, 2)
            k = attn.key(xk_intermediate).view(B, T, H, head_dim).transpose(1, 2)
            v = attn.value(xv_intermediate).view(B, T, H, head_dim).transpose(1, 2)

            # Attention computation
            attn_mask = None
            if attention_mask is not None:
                key_pad = (~attention_mask.bool())
                additive = key_pad[:, None, None, :].to(dtype=torch.float32)
                additive = additive.masked_fill(additive > 0, float('-inf')).masked_fill(additive == 0, 0.0)
                attn_mask = additive
            y = F.scaled_dot_product_attention(q, k, v, attn_mask=attn_mask, is_causal=False)
            y = y.transpose(1, 2).contiguous().view(B, T, C)
            
            # DO NOT apply c_proj here - extract CLS tokens before projection
            x_pre_proj = x + y  # residual connection without c_proj
            
            # Apply MLP
            x_pre_proj = x_pre_proj + final_block.mlp(final_block.ln_2(x_pre_proj), attention_mask=attention_mask)
        else:
            x_pre_proj = x
            
        # Apply final layer norm
        x_pre_proj = model.transformer.ln_f(x_pre_proj)
        
        # Extract and concatenate CLS tokens (before final c_proj)
        cls_tokens = x_pre_proj[:, :16, :]  # (B, 16, C)
        if normalize:
            cls_tokens = F.normalize(cls_tokens, p=2, dim=-1)  # Normalize each CLS vector individually
        pooled = cls_tokens.view(cls_tokens.size(0), -1)  # (B, 16*C)
        
        if debug:
            cls_std = torch.std(cls_tokens[0], dim=0).mean().item()
            concat_norm = torch.norm(pooled[0]).item()
            print(f"[Debug] Pre-proj CLS std: {cls_std:.6f}, Concat norm: {concat_norm:.6f}")
        
        return pooled
    
    # Standard processing for other pooling methods
    for block in model.transformer.h:
        x = block(x, attention_mask=attention_mask)
    x = model.transformer.ln_f(x)
    
    if debug:
        cls_after_transformer = x[:, :16, :]  # CLS tokens after transformer
        print(f"[Debug] After transformer - ||CLS1 - CLS2||: {torch.norm(cls_after_transformer[0,0] - cls_after_transformer[0,1]).item():.6f}")
        
        # Check if all 16 CLS hidden states became identical
        first_cls = cls_after_transformer[0, 0]
        all_identical = True
        for i in range(1, 16):
            if not torch.allclose(first_cls, cls_after_transformer[0, i], atol=1e-6):
                all_identical = False
                break
        print(f"[Debug] All 16 CLS hidden states identical after transformer: {all_identical}")
    
    if pooling == "cls_avg":
        # Average of all 16 CLS tokens
        cls_tokens = x[:, :16, :]  # (B, 16, C)
        pooled = cls_tokens.mean(dim=1)  # (B, C)
        
        if debug:
            # Debug: Check variance in CLS tokens before averaging
            cls_std = torch.std(cls_tokens[0], dim=0).mean().item()
            print(f"[Debug] Average std deviation across CLS token dimensions: {cls_std:.6f}")
            
            # Show what averaging does to the representation
            first_cls_norm = torch.norm(cls_tokens[0, 0]).item()
            avg_norm = torch.norm(pooled[0]).item()
            print(f"[Debug] First CLS norm: {first_cls_norm:.6f}, Average CLS norm: {avg_norm:.6f}")
            
    elif pooling == "cls_concat":
        # Concatenate all 16 CLS tokens (normalize each individually first)
        cls_tokens = x[:, :16, :]  # (B, 16, C)
        if normalize:
            cls_tokens = F.normalize(cls_tokens, p=2, dim=-1)  # Normalize each CLS vector individually
        pooled = cls_tokens.view(cls_tokens.size(0), -1)  # (B, 16*C)
        
    elif pooling == "first_last_cls_avg":
        # Average of first and last CLS tokens (CLS1 and CLS16)
        cls_tokens = x[:, :16, :]  # (B, 16, C)
        first_cls = cls_tokens[:, 0, :]  # (B, C) - CLS1
        last_cls = cls_tokens[:, 15, :]  # (B, C) - CLS16
        pooled = (first_cls + last_cls) / 2.0  # (B, C)
        
        if debug:
            first_norm = torch.norm(first_cls[0]).item()
            last_norm = torch.norm(last_cls[0]).item()
            avg_norm = torch.norm(pooled[0]).item()
            diff_norm = torch.norm(first_cls[0] - last_cls[0]).item()
            print(f"[Debug] First CLS norm: {first_norm:.6f}, Last CLS norm: {last_norm:.6f}")
            print(f"[Debug] First-Last CLS difference norm: {diff_norm:.6f}")
            print(f"[Debug] Average First-Last CLS norm: {avg_norm:.6f}")
            
    elif pooling == "cls_weighted":
        # Similarity-based weighted average of CLS tokens using cls_weight_proj
        cls_tokens = x[:, :16, :]  # (B, 16, C)
        
        # Fast learned weights from CLS tokens directly
        # Compute weights from CLS tokens themselves (B, 16, 1) -> (B, 16)
        raw_weights = model.cls_weight_proj(cls_tokens).squeeze(-1)  # (B, 16)
        weights = F.softmax(raw_weights, dim=-1)  # (B, 16)
        
        # Efficient weighted sum using einsum
        pooled = torch.einsum('bi,bic->bc', weights, cls_tokens)  # (B, C)
        
        if debug:
            # Debug: Show weight distribution
            weights_std = torch.std(weights[0]).item()
            max_weight = torch.max(weights[0]).item()
            min_weight = torch.min(weights[0]).item()
            print(f"[Debug] Weight distribution - std: {weights_std:.6f}, max: {max_weight:.6f}, min: {min_weight:.6f}")
            print(f"[Debug] Top 3 weights: {torch.topk(weights[0], 3).values.tolist()}")
            
            # Show what weighting does to the representation
            avg_cls_norm = torch.norm(cls_tokens[0].mean(dim=0)).item()
            weighted_norm = torch.norm(pooled[0]).item()
            print(f"[Debug] Average CLS norm: {avg_cls_norm:.6f}, Weighted CLS norm: {weighted_norm:.6f}")
            
    elif pooling == "mean":
        # mean over non-pad tokens
        mask = attention_mask.to(x.dtype)  # (B, T)
        denom = mask.sum(dim=1, keepdim=True).clamp_min(1.0)  # (B,1)
        pooled = (x * mask.unsqueeze(-1)).sum(dim=1) / denom
    else:
        pooled = x[:, 0, :]
    
    if normalize and pooling not in ["cls_concat", "cls_concat_pre_proj"]:
        # Skip final normalization for cls_concat methods since we already normalized each vector individually
        pooled = F.normalize(pooled, p=2, dim=-1)
    return pooled


def get_multicls_embeddings(model: MaskedLanguageModel, input_ids: torch.Tensor, attention_mask: torch.Tensor, normalize: bool = True):
    """Get all 16 CLS embeddings separately for multi-vector reranking."""
    # Handle embeddings: regular tokens use wte, CLS tokens use cls_embeddings
    B, T = input_ids.size()
    x = torch.zeros(B, T, model.config.n_embd, device=input_ids.device, dtype=model.transformer.wte.weight.dtype)
    
    # Regular tokens (including MASK, PAD)
    regular_mask = input_ids < (model.config.vocab_size + 2)  # vocab + MASK + PAD
    if regular_mask.any():
        x[regular_mask] = model.transformer.wte(input_ids[regular_mask])
    
    # CLS tokens
    cls_token_ids = list(range(model.config.vocab_size + 2, model.config.vocab_size + 2 + model.config.n_cls_tokens))
    for i, cls_id in enumerate(cls_token_ids):
        cls_mask = (input_ids == cls_id)
        if cls_mask.any():
            x[cls_mask] = model.cls_embeddings[i]
    for block in model.transformer.h:
        x = block(x, attention_mask=attention_mask)
    x = model.transformer.ln_f(x)
    
    # Return all 16 CLS tokens separately
    cls_tokens = x[:, :16, :]  # (B, 16, C)
    
    if normalize:
        cls_tokens = F.normalize(cls_tokens, p=2, dim=-1)
    return cls_tokens


def compute_results_single_stage(queries: Dict[str, str], corpus: Dict[str, Dict[str, str]], qrels: Dict[str, Dict[str, int]],
                    model: MaskedLanguageModel, tokenizer, pad_id: int, batch_size: int, max_len: int, device: torch.device, effective_vocab_size: int, cls_ids: List[int], pooling: str = "cls_avg") -> Dict[str, Dict[str, float]]:
    """Single-stage retrieval using specified pooling method."""
    # Encode corpus to embeddings
    doc_ids = list(corpus.keys())
    doc_texts = [corpus[did]["text"] if corpus[did].get("title") is None else (corpus[did]["title"] + " \n" + corpus[did]["text"]) for did in doc_ids]
    all_doc_embs = []
    
    model.eval()
    with torch.no_grad():
        for i in range(0, len(doc_texts), batch_size):
            batch_texts = doc_texts[i:i+batch_size]
            input_ids, attn = batch_encode_with_multicls(tokenizer, batch_texts, pad_id, max_len, device, effective_vocab_size, cls_ids)
            embs = get_cls_embeddings(model, input_ids, attn, normalize=True, pooling=pooling)
            all_doc_embs.append(embs)
    
    doc_matrix = torch.cat(all_doc_embs, dim=0)  # [N_docs, dim]

    # Encode queries and compute scores
    results: Dict[str, Dict[str, float]] = {}
    with torch.no_grad():
        for i, (qid, qtext) in enumerate(queries.items()):
            q_input, q_attn = batch_encode_with_multicls(tokenizer, [qtext], pad_id, max_len, device, effective_vocab_size, cls_ids)
            q_emb = get_cls_embeddings(model, q_input, q_attn, normalize=True, pooling=pooling)  # [1, dim]
            # cosine similarity via dot product of normalized vectors
            scores = torch.matmul(q_emb, doc_matrix.T).squeeze(0)  # [N_docs]
            # Convert to dict of top scores (all docs). For efficiency, take top 1000.
            topk = min(1000, scores.size(0))
            vals, idxs = torch.topk(scores, k=topk, largest=True)
            res = {doc_ids[j.item()]: float(vals[k].item()) for k, j in enumerate(idxs)}
            results[qid] = res
    return results


def compute_results_two_stage(queries: Dict[str, str], corpus: Dict[str, Dict[str, str]], qrels: Dict[str, Dict[str, int]],
                    model: MaskedLanguageModel, tokenizer, pad_id: int, batch_size: int, max_len: int, device: torch.device, 
                    effective_vocab_size: int, cls_ids: List[int], top_k: int = 100) -> Dict[str, Dict[str, float]]:
    """Two-stage retrieval: first use cls_avg for initial retrieval, then rerank top-k with multi-vector CLS."""
    print(f"[Two-Stage] Stage 1: Initial retrieval with cls_avg, retrieving top-{top_k} candidates")
    
    # Stage 1: Initial retrieval with cls_avg pooling
    doc_ids = list(corpus.keys())
    doc_texts = [corpus[did]["text"] if corpus[did].get("title") is None else (corpus[did]["title"] + " \n" + corpus[did]["text"]) for did in doc_ids]
    
    # Encode corpus with cls_avg pooling
    all_doc_embs_avg = []
    model.eval()
    with torch.no_grad():
        for i in range(0, len(doc_texts), batch_size):
            batch_texts = doc_texts[i:i+batch_size]
            input_ids, attn = batch_encode_with_multicls(tokenizer, batch_texts, pad_id, max_len, device, effective_vocab_size, cls_ids)
            embs = get_cls_embeddings(model, input_ids, attn, normalize=True, pooling="cls_avg")
            all_doc_embs_avg.append(embs)
    
    doc_matrix_avg = torch.cat(all_doc_embs_avg, dim=0)  # [N_docs, dim]
    
    # Get initial top-k candidates for each query using cls_avg
    initial_candidates = {}
    with torch.no_grad():
        for qid, qtext in queries.items():
            q_input, q_attn = batch_encode_with_multicls(tokenizer, [qtext], pad_id, max_len, device, effective_vocab_size, cls_ids)
            q_emb_avg = get_cls_embeddings(model, q_input, q_attn, normalize=True, pooling="cls_avg")  # [1, dim]
            
            # Get top-k candidates
            scores = torch.matmul(q_emb_avg, doc_matrix_avg.T).squeeze(0)  # [N_docs]
            topk_actual = min(top_k, scores.size(0))
            vals, idxs = torch.topk(scores, k=topk_actual, largest=True)
            
            # Store candidate doc IDs and their indices
            candidate_doc_ids = [doc_ids[j.item()] for j in idxs]
            candidate_indices = [j.item() for j in idxs]
            initial_candidates[qid] = {
                'doc_ids': candidate_doc_ids,
                'indices': candidate_indices,
                'texts': [doc_texts[idx] for idx in candidate_indices]
            }
    
    print(f"[Two-Stage] Stage 2: Reranking top-{top_k} candidates with multi-vector CLS (16 vectors)")
    
    # Stage 2: Rerank candidates with multi-vector CLS representations
    results: Dict[str, Dict[str, float]] = {}
    
    with torch.no_grad():
        for qid, qtext in queries.items():
            candidates = initial_candidates[qid]
            candidate_texts = candidates['texts']
            candidate_doc_ids = candidates['doc_ids']
            
            if not candidate_texts:
                results[qid] = {}
                continue
            
            # Get query multi-vector representation (16 CLS tokens)
            q_input, q_attn = batch_encode_with_multicls(tokenizer, [qtext], pad_id, max_len, device, effective_vocab_size, cls_ids)
            q_multicls = get_multicls_embeddings(model, q_input, q_attn, normalize=True)  # [1, 16, dim]
            q_multicls = q_multicls.squeeze(0)  # [16, dim]
            
            # Get candidate multi-vector representations
            all_candidate_multicls = []
            for i in range(0, len(candidate_texts), batch_size):
                batch_texts = candidate_texts[i:i+batch_size]
                input_ids, attn = batch_encode_with_multicls(tokenizer, batch_texts, pad_id, max_len, device, effective_vocab_size, cls_ids)
                multicls = get_multicls_embeddings(model, input_ids, attn, normalize=True)  # [batch_size, 16, dim]
                all_candidate_multicls.append(multicls)
            
            candidate_multicls = torch.cat(all_candidate_multicls, dim=0)  # [n_candidates, 16, dim]
            
            # Compute multi-vector similarity scores
            # For each query-doc pair, compute max similarity across all CLS token pairs
            rerank_scores = []
            for doc_idx in range(candidate_multicls.size(0)):
                doc_multicls = candidate_multicls[doc_idx]  # [16, dim]
                
                # Compute similarity matrix between query CLS tokens and doc CLS tokens
                sim_matrix = torch.matmul(q_multicls, doc_multicls.T)  # [16, 16]
                
                # Take maximum similarity (best matching CLS token pair)
                max_sim = torch.max(sim_matrix).item()
                rerank_scores.append(max_sim)
            
            # Create final results with reranked scores
            final_results = {}
            for doc_id, score in zip(candidate_doc_ids, rerank_scores):
                final_results[doc_id] = score
            
            # Sort by reranked scores
            sorted_results = dict(sorted(final_results.items(), key=lambda x: x[1], reverse=True))
            results[qid] = sorted_results
    
    return results


def evaluate_two_stage_pooling(queries, corpus, qrels, model, tokenizer, pad_id, batch_size, max_len, device, effective_vocab_size, cls_ids, top_k=100):
    """Evaluate two-stage pooling method and return metrics."""
    print(f"\n{'='*60}")
    print(f"Evaluating two-stage pooling: cls_avg -> multi-vector rerank (top-{top_k})")
    print(f"{'='*60}")
    
    # Compute results for two-stage approach
    results = compute_results_two_stage(queries, corpus, qrels, model, tokenizer, pad_id, batch_size, max_len, device, effective_vocab_size, cls_ids, top_k=top_k)
    
    # Manual evaluation to bypass BEIR issues
    def compute_manual_metrics(qrels, results, k_values):
        metrics = {k: {'ndcg': [], 'recall': [], 'precision': []} for k in k_values}
        
        for qid in qrels:
            if qid not in results:
                continue
                
            # Get top-k results and relevant docs
            ranked_docs = list(results[qid].keys())
            relevant_docs = set(qrels[qid].keys())
            
            for k in k_values:
                top_k_docs = ranked_docs[:k]
                relevant_in_k = [doc for doc in top_k_docs if doc in relevant_docs]
                
                # Recall@k
                recall_k = len(relevant_in_k) / len(relevant_docs) if relevant_docs else 0.0
                metrics[k]['recall'].append(recall_k)
                
                # Precision@k  
                precision_k = len(relevant_in_k) / k if k > 0 else 0.0
                metrics[k]['precision'].append(precision_k)
                
                # Simple NDCG@k (binary relevance)
                dcg = sum(1.0 / math.log2(i + 2) for i, doc in enumerate(top_k_docs) if doc in relevant_docs)
                idcg = sum(1.0 / math.log2(i + 2) for i in range(min(k, len(relevant_docs))))
                ndcg_k = dcg / idcg if idcg > 0 else 0.0
                metrics[k]['ndcg'].append(ndcg_k)
        
        # Average across queries
        avg_metrics = {}
        for k in k_values:
            avg_metrics[k] = {
                'ndcg': sum(metrics[k]['ndcg']) / len(metrics[k]['ndcg']) if metrics[k]['ndcg'] else 0.0,
                'recall': sum(metrics[k]['recall']) / len(metrics[k]['recall']) if metrics[k]['recall'] else 0.0,
                'precision': sum(metrics[k]['precision']) / len(metrics[k]['precision']) if metrics[k]['precision'] else 0.0
            }
        return avg_metrics
    
    # Compute manual metrics
    k_values = [1, 3, 5, 10, 100]
    manual_metrics = compute_manual_metrics(qrels, results, k_values)
    
    # Add diagnostics to understand performance
    total_queries = len(queries)
    queries_with_hits_10 = 0
    queries_with_hits_100 = 0
    
    # Count overall hit rates
    for qid in queries:
        if qid in results and qid in qrels:
            top_10 = set(list(results[qid].keys())[:10])
            top_100 = set(list(results[qid].keys())[:100])
            relevant = set(qrels[qid].keys())
            
            if top_10 & relevant:
                queries_with_hits_10 += 1
            if top_100 & relevant:
                queries_with_hits_100 += 1
    
    print(f"Queries with hits@10: {queries_with_hits_10}/{total_queries} ({100*queries_with_hits_10/total_queries:.1f}%)")
    print(f"Queries with hits@100: {queries_with_hits_100}/{total_queries} ({100*queries_with_hits_100/total_queries:.1f}%)")
    
    print(f"\nMetrics for two-stage pooling:")
    print(f"NDCG@10: {manual_metrics[10]['ndcg']:.4f}")
    for k in k_values:
        nd = manual_metrics[k]['ndcg']
        rc = manual_metrics[k]['recall'] 
        pr = manual_metrics[k]['precision']
        print(f"k={k}: NDCG={nd:.4f}, Recall={rc:.4f}, P@k={pr:.4f}")
    
    return manual_metrics


def evaluate_single_pooling(queries, corpus, qrels, model, tokenizer, pad_id, batch_size, max_len, device, effective_vocab_size, cls_ids, pooling_method):
    """Evaluate a single pooling method and return metrics."""
    print(f"\n{'='*60}")
    print(f"Evaluating pooling method: {pooling_method}")
    print(f"{'='*60}")
    
    # Compute results for this pooling method
    results = compute_results_single_stage(queries, corpus, qrels, model, tokenizer, pad_id, batch_size, max_len, device, effective_vocab_size, cls_ids, pooling=pooling_method)
    
    # Manual evaluation to bypass BEIR issues
    def compute_manual_metrics(qrels, results, k_values):
        metrics = {k: {'ndcg': [], 'recall': [], 'precision': []} for k in k_values}
        
        for qid in qrels:
            if qid not in results:
                continue
                
            # Get top-k results and relevant docs
            ranked_docs = list(results[qid].keys())
            relevant_docs = set(qrels[qid].keys())
            
            for k in k_values:
                top_k = ranked_docs[:k]
                relevant_in_k = [doc for doc in top_k if doc in relevant_docs]
                
                # Recall@k
                recall_k = len(relevant_in_k) / len(relevant_docs) if relevant_docs else 0.0
                metrics[k]['recall'].append(recall_k)
                
                # Precision@k  
                precision_k = len(relevant_in_k) / k if k > 0 else 0.0
                metrics[k]['precision'].append(precision_k)
                
                # Simple NDCG@k (binary relevance)
                dcg = sum(1.0 / math.log2(i + 2) for i, doc in enumerate(top_k) if doc in relevant_docs)
                idcg = sum(1.0 / math.log2(i + 2) for i in range(min(k, len(relevant_docs))))
                ndcg_k = dcg / idcg if idcg > 0 else 0.0
                metrics[k]['ndcg'].append(ndcg_k)
        
        # Average across queries
        avg_metrics = {}
        for k in k_values:
            avg_metrics[k] = {
                'ndcg': sum(metrics[k]['ndcg']) / len(metrics[k]['ndcg']) if metrics[k]['ndcg'] else 0.0,
                'recall': sum(metrics[k]['recall']) / len(metrics[k]['recall']) if metrics[k]['recall'] else 0.0,
                'precision': sum(metrics[k]['precision']) / len(metrics[k]['precision']) if metrics[k]['precision'] else 0.0
            }
        return avg_metrics
    
    # Compute manual metrics
    k_values = [1, 3, 5, 10, 100]
    manual_metrics = compute_manual_metrics(qrels, results, k_values)
    
    # Add diagnostics to understand performance
    total_queries = len(queries)
    queries_with_hits_10 = 0
    queries_with_hits_100 = 0
    
    # Count overall hit rates
    for qid in queries:
        if qid in results and qid in qrels:
            top_10 = set(list(results[qid].keys())[:10])
            top_100 = set(list(results[qid].keys())[:100])
            relevant = set(qrels[qid].keys())
            
            if top_10 & relevant:
                queries_with_hits_10 += 1
            if top_100 & relevant:
                queries_with_hits_100 += 1
    
    print(f"Queries with hits@10: {queries_with_hits_10}/{total_queries} ({100*queries_with_hits_10/total_queries:.1f}%)")
    print(f"Queries with hits@100: {queries_with_hits_100}/{total_queries} ({100*queries_with_hits_100/total_queries:.1f}%)")
    
    print(f"\nMetrics for {pooling_method}:")
    print(f"NDCG@10: {manual_metrics[10]['ndcg']:.4f}")
    for k in k_values:
        nd = manual_metrics[k]['ndcg']
        rc = manual_metrics[k]['recall'] 
        pr = manual_metrics[k]['precision']
        print(f"k={k}: NDCG={nd:.4f}, Recall={rc:.4f}, P@k={pr:.4f}")
    
    return manual_metrics


def main():
    parser = argparse.ArgumentParser(description="Evaluate Multi-CLS embeddings with different pooling strategies on BEIR dataset")
    parser.add_argument("--checkpoint", type=str, default="/home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/log_encoder_mlm_fineweb_10B_pure_multi_cls/model_19999.pt", help="Path to model checkpoint (.pt) with state_dict under key 'model' or raw state_dict")
    parser.add_argument("--tokenizer", type=str, default="/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls", help="Path to tokenizer directory")
    parser.add_argument("--dataset", type=str, default="scifact", help="BEIR dataset name, e.g., scifact, trec-covid, nfcorpus, fiqa, dbpedia-entity, arguana, scidocs")
    parser.add_argument("--data_dir", type=str, default="./beir_datasets", help="Directory to download/extract BEIR datasets")
    parser.add_argument("--batch_size", type=int, default=64)
    parser.add_argument("--max_len", type=int, default=1024)
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu")
    parser.add_argument("--pooling", type=str, choices=["cls_avg", "cls_weighted", "cls_concat", "cls_concat_pre_proj", "first_last_cls_avg", "two_stage", "all"], default="all", help="Pooling strategy for fixed-size embeddings")
    parser.add_argument("--rerank_top_k", type=int, default=20, help="Number of top candidates to rerank in two-stage approach")
    args = parser.parse_args()

    if Evaluate is None:
        print(f"BEIR evaluation import failed: {_eval_import_error}. Try: pip install pytrec-eval-terrier or pip install 'beir[eval]'")
        sys.exit(1)

    # Download & load dataset
    os.makedirs(args.data_dir, exist_ok=True)
    dataset_url = util.download_and_unzip("https://public.ukp.informatik.tu-darmstadt.de/thakur/BEIR/datasets/" + args.dataset + ".zip", args.data_dir)
    data_folder = os.path.join(args.data_dir, args.dataset)
    corpus, queries, qrels = GenericDataLoader(data_folder).load(split="test")

    # Load checkpoint first to get the correct vocabulary size
    device = torch.device(args.device)
    print(f"[Info] Loading checkpoint from: {args.checkpoint}")
    # Use weights_only=False to handle custom classes like EncoderConfig
    ckpt = torch.load(args.checkpoint, map_location=device, weights_only=False)
    state_dict = ckpt.get("model", ckpt)

    # Handle compiled models (torch.compile adds _orig_mod. prefix)
    if any(key.startswith("_orig_mod.") for key in state_dict.keys()):
        print("[Info] Detected compiled model checkpoint, removing _orig_mod. prefix")
        state_dict = {key.replace("_orig_mod.", ""): value for key, value in state_dict.items()}

    # Get vocabulary size from checkpoint
    checkpoint_vocab_size = state_dict["transformer.wte.weight"].shape[0]
    print(f"[Info] Checkpoint vocabulary size: {checkpoint_vocab_size}")

    # Build model with checkpoint's vocabulary size
    config = load_model_config_from_yaml("config.yaml")

    # For multi-CLS models, the checkpoint vocab size includes the base vocab + special tokens
    # We need to back-calculate the base vocab size
    if config.use_multi_cls:
        # effective_vocab_size = config.vocab_size + 4 + n_cls_tokens - 1
        # So: config.vocab_size = checkpoint_vocab_size - 4 - n_cls_tokens + 1
        base_vocab_size = checkpoint_vocab_size - 4 - config.n_cls_tokens + 1
        config.vocab_size = base_vocab_size
        print(f"[Debug] Multi-CLS model: adjusted base vocab_size to: {config.vocab_size}")
    else:
        config.vocab_size = checkpoint_vocab_size
        print(f"[Debug] Standard model: vocab_size set to: {config.vocab_size}")

    print(f"[Debug] Config use_multi_cls: {config.use_multi_cls}")
    model = MaskedLanguageModel(config).to(device)
    print(f"[Debug] Model transformer.wte.weight.shape: {model.transformer.wte.weight.shape}")

    # Load the state dict
    missing, unexpected = model.load_state_dict(state_dict, strict=False)
    if missing:
        print(f"[Warn] Missing keys: {len(missing)} e.g., {missing[:5]}")
    if unexpected:
        print(f"[Warn] Unexpected keys: {len(unexpected)} e.g., {unexpected[:5]}")

    model.eval()

    # Debug: Check if 16 CLS embeddings are actually different
    print("\n[Debug] Checking CLS token embeddings...")
    with torch.no_grad():
        # CLS tokens are stored in model.cls_embeddings, not in wte
        cls_embeddings = model.cls_embeddings  # Shape: (16, n_embd)
        
        print(f"[Debug] CLS embeddings shape: {cls_embeddings.shape}")
        print(f"[Debug] Regular vocab embedding shape: {model.transformer.wte.weight.shape}")
        
        # Check if all CLS embeddings are identical
        first_cls = cls_embeddings[0]
        all_identical = True
        for i in range(1, 16):
            if not torch.allclose(first_cls, cls_embeddings[i], atol=1e-6):
                all_identical = False
                break
        
        print(f"[Debug] All 16 CLS embeddings identical: {all_identical}")
        if not all_identical:
            # Show some differences
            diff_norm = torch.norm(cls_embeddings[0] - cls_embeddings[1]).item()
            print(f"[Debug] ||CLS1 - CLS2|| = {diff_norm:.6f}")
            diff_norm = torch.norm(cls_embeddings[0] - cls_embeddings[15]).item()
            print(f"[Debug] ||CLS1 - CLS16|| = {diff_norm:.6f}")
        print()

    # Tokenizer aligned with model's special token ids
    tokenizer, mask_id, pad_id, cls_ids, effective_vocab_size = build_tokenizer(config.vocab_size, args.tokenizer)
    
    # Update effective vocab size for 16 CLS tokens
    effective_vocab_size = config.vocab_size + 2 + 16  # MASK + PAD + 16*CLS

    # Debug: Test with first query to see CLS behavior
    print("[Debug] Testing first query to check CLS hidden states...")
    first_query = list(queries.items())[0][1]
    test_input, test_attn = batch_encode_with_multicls(tokenizer, [first_query], pad_id, args.max_len, device, effective_vocab_size, cls_ids)
    
    # Debug: Show actual input sequence
    print(f"[Debug] First query: '{first_query[:50]}...'")
    print(f"[Debug] Input token IDs (first 20): {test_input[0][:20].tolist()}")
    print(f"[Debug] Expected CLS tokens: {cls_ids}")
    print(f"[Debug] Are first 16 tokens the CLS tokens? {test_input[0][:16].tolist() == cls_ids}")
    
    # Define pooling methods to evaluate
    if args.pooling == "all":
        pooling_methods = ["cls_avg", "cls_weighted", "cls_concat", "cls_concat_pre_proj", "first_last_cls_avg", "two_stage"]
    else:
        pooling_methods = [args.pooling]
    
    # Store all results for comparison
    all_results = {}
    
    # Test each pooling method
    for pooling_method in pooling_methods:
        if pooling_method == "two_stage":
            print(f"\nTesting two-stage pooling with top-{args.rerank_top_k} reranking...")
            # Evaluate two-stage approach
            metrics = evaluate_two_stage_pooling(queries, corpus, qrels, model, tokenizer, pad_id, args.batch_size, args.max_len, device, effective_vocab_size, cls_ids, top_k=args.rerank_top_k)
            all_results[pooling_method] = metrics
        else:
            print(f"\nTesting {pooling_method} with debug info...")
            _ = get_cls_embeddings(model, test_input, test_attn, normalize=True, pooling=pooling_method, debug=True)
            
            # Evaluate this pooling method
            metrics = evaluate_single_pooling(queries, corpus, qrels, model, tokenizer, pad_id, args.batch_size, args.max_len, device, effective_vocab_size, cls_ids, pooling_method)
            all_results[pooling_method] = metrics
    
    # Print comparison summary
    if len(pooling_methods) > 1:
        print(f"\n{'='*80}")
        print("COMPARISON SUMMARY")
        print(f"{'='*80}")
        print(f"{'Method':<20} {'NDCG@1':<10} {'NDCG@5':<10} {'NDCG@10':<10} {'Recall@10':<12} {'Recall@100':<12}")
        print("-" * 80)
        for method in pooling_methods:
            metrics = all_results[method]
            print(f"{method:<20} {metrics[1]['ndcg']:<10.4f} {metrics[5]['ndcg']:<10.4f} {metrics[10]['ndcg']:<10.4f} {metrics[10]['recall']:<12.4f} {metrics[100]['recall']:<12.4f}")
        
        # Find best method for key metrics
        best_ndcg10 = max(pooling_methods, key=lambda m: all_results[m][10]['ndcg'])
        best_recall10 = max(pooling_methods, key=lambda m: all_results[m][10]['recall'])
        
        print(f"\nBest NDCG@10: {best_ndcg10} ({all_results[best_ndcg10][10]['ndcg']:.4f})")
        print(f"Best Recall@10: {best_recall10} ({all_results[best_recall10][10]['recall']:.4f})")


if __name__ == "__main__":
    main()
