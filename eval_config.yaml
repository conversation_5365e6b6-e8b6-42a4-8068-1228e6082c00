# Evaluation Configuration for BEIR datasets

# Model configuration
model:
  block_size: 1024
  # vocab_size: 50368
  n_layer: 12
  n_head: 12
  n_embd: 768 
  n_head_4: 12
  mask_prob: 0.15

# Data configuration
data:
  tokenizer_path: "/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls"

# Evaluation configuration
evaluation:
  checkpoint: /home/<USER>/notebooks/Ali_embedding/Ali_embedding/log_encoder_mlm_pile_20B_pure_lower_special_tokens_lr_batchprob_new/model_19999.pt 
  dataset: "arguana"  # scifact, trec-covid, nfcorpus, fiqa, dbpedia-entity, arguana, scidocs
  data_dir: /home/<USER>/notebooks/Ali_models/beir_datasets
  eval_dir: "eval"  # Directory to save evaluation results
  batch_size: 64
  max_len: 1024
  device: "cuda"
  pooling: "cls_eos_avg"  # cls, mean
  prepend_cls: False
