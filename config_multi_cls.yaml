# Training Configuration for Encoder MLM Model
model:
  block_size: 1024
  vocab_size: 50368  # Will be overridden by tokenizer
  n_layer: 12
  n_head: 12
  n_embd: 768
  n_head_4: 12
  mask_prob: 0.15
  # Multi-CLS token configuration
  use_multi_cls: true   # Choose between CLS+EOS (false) or multi-CLS (true)
  n_cls_tokens: 16      # Number of CLS tokens when use_multi_cls is true
  cls_embedding_lr_scale: 0.1  # Learning rate scale for CLS embeddings (when use_multi_cls is true)

training:
  total_batch_size: 1048576  # 2**20, ~1M tokens (matches script default)
  micro_batch_size: 32
  sequence_length: 1024
  max_steps: 20000  # Updated to match script default
  warmup_steps: 400   # Updated to match script default
  max_lr: 0.0002       # Updated to match script default
  min_lr: 0.00002       # max_lr * 0.1
  weight_decay: 0.1
  grad_clip_norm: 1.0
  use_compile: true
  gradient_checkpointing: false  # Enable for memory efficiency with large models
  optimizer_lr: 0.0006  # Base optimizer learning rate
  embedding_lr_scale: 0.1  # Embedding layer learning rate scale
  
data:
  # Updated to match script's expected format
  dataset_paths: ["/s2_nfs/fineweb-edu/localds/sample-10BT/"]  # Array format for multiple datasets
  tokenizer_path: "/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls"
  target_tokens: 10000000000  # 20B tokens
  dataset_name: "finweb 10B"  # Display name for logging and prints
  num_workers: 8            # Increased from 8 for better I/O parallelism
  prefetch_factor: 4         # Increased from 4 for better buffering
  # pin_memory: true           # Add for faster GPU transfers
  # persistent_workers: true   # Keep workers alive between epochs
  
logging:
  writer_dir_path: "./log_encoder_fineweb_multi_cls/"
  run_group_name: "encoder_mlm_124M_fineweb_10B_multi_cls"
  run_no: "fineweb_sampling_10B"
  log_dir: "log_encoder_mlm_fineweb_10B_pure_multi_cls"
  log_file: "log_fineweb_10B_training_multi_cls.txt"
  log_interval: 500  # Updated to match script default
  val_loss_steps: 20
  
saving:
  enabled: true
  save_interval: 5000  # Save at steps 5000, 10000, 15000
  save_on_last_step: true
  checkpoint_dir: "checkpoints_multi_cls"
  keep_last_n: 3  # Keep only last N checkpoints
  save_optimizer: true
  save_scheduler: false
  save_best_only: false  # If true, only save when validation loss improves
  
wandb:
  enabled: true
  project: "encoder-mlm-ali"
  entity: null  # Your wandb username/team
  name: null  # Will use run_name if not specified
  tags: ["encoder", "mlm", "time-mixing", "fineweb", "10B", "multi_cls"]
  notes: "Encoder with time-mixing attention and CLS-gated predictions on fineweb 20B"
  log_interval: 100  # Log metrics every N steps
  watch_model: false  # Watch model gradients and parameters
  watch_log: "gradients"  # "gradients", "parameters", "all", or null
  
device:
  auto_detect: true
  preferred: "cuda"  # "cuda", "cpu", "mps"
  mixed_precision: true
  dtype: "bfloat16"  # "float32", "float16", "bfloat16"
