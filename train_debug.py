import os
import math
import time
import datetime
from dataclasses import dataclass
import torch
from torch.distributed import init_process_group, destroy_process_group
from torch.nn.parallel import DistributedDataParallel as DDP
import torch.distributed as dist
from torch.utils.tensorboard import SummaryWriter
from transformers import AutoTokenizer

from models import load_all_configs_from_yaml, LoggingConfig, DataConfig, TrainingConfig, ModelConfig
from models.masked_language_model_debug import MaskedLanguageModelDebug
from data import DataLoaderLite
from training import evaluate_mlm_loss
from MyTokenizer import AutoCLSTokenizer, is_qwen_tokenizer


def main():
    """Main training function with debugging"""
    print("DEBUG: Starting debug training script")
    
    # Load all configurations from YAML in single read
    logging_config, data_config, training_config, model_config = load_all_configs_from_yaml("config.yaml")
    
    # Logging setup using config
    writer = SummaryWriter(log_dir=f"{logging_config.writer_dir_path}/{logging_config.run_group_name}/{logging_config.run_no}")
    
    # Set up DDP (distributed data parallel)
    ddp = int(os.environ.get('RANK', -1)) != -1 # is this a ddp run?
    if ddp:
        # use of DDP atm demands CUDA, we set the device appropriately according to rank
        assert torch.cuda.is_available(), "for now i think we need CUDA for DDP"
        init_process_group(backend='nccl')
        ddp_rank = int(os.environ['RANK'])
        ddp_local_rank = int(os.environ['LOCAL_RANK'])
        ddp_world_size = int(os.environ['WORLD_SIZE'])
        device = f'cuda:{ddp_local_rank}'
        torch.cuda.set_device(device)
        master_process = ddp_rank == 0 # this process will do logging, checkpointing etc.
    else:
        # vanilla, non-DDP run
        ddp_rank = 0
        ddp_local_rank = 0
        ddp_world_size = 1
        master_process = True
        # attempt to autodetect device
        device = "cpu"
        if torch.cuda.is_available():
            device = "cuda"
        elif hasattr(torch.backends, "mps") and torch.backends.mps.is_available():
            device = "mps"
        print(f"using device: {device}")
    
    device_type = "cuda" if device.startswith("cuda") else "cpu"
    
    # Set random seeds
    torch.manual_seed(1337)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(1337)
    
    # Initialize tokenizer from config
    if is_qwen_tokenizer(data_config.tokenizer_path):
        tokenizer = AutoCLSTokenizer(data_config.tokenizer_path)
    else:
        tokenizer = AutoTokenizer.from_pretrained(data_config.tokenizer_path)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    if tokenizer.cls_token is None:
        tokenizer.add_special_tokens({'cls_token': '[CLS]'})
    if tokenizer.mask_token is None:
        tokenizer.add_special_tokens({'mask_token': '[MASK]'})
    
    print(f"Tokenizer vocab size: {len(tokenizer)}")
    print(f"DEBUG: Special tokens - CLS: {tokenizer.cls_token_id}, EOS: {tokenizer.eos_token_id}, MASK: {tokenizer.mask_token_id}, PAD: {tokenizer.pad_token_id}")
    
    # Training hyperparameters from config
    total_batch_size = training_config.total_batch_size
    B = training_config.micro_batch_size
    T = training_config.sequence_length
    assert total_batch_size % (B * T * ddp_world_size) == 0, "make sure total_batch_size is divisible by B * T * ddp_world_size"
    grad_accum_steps = total_batch_size // (B * T * ddp_world_size)
    if master_process:
        print(f"total desired batch size: {total_batch_size}")
        print(f"micro batch size: {B}")
        print(f"=> calculated gradient accumulation steps: {grad_accum_steps}")
    
    # Initialize data loaders using config and shared tokenizer
    train_loader = DataLoaderLite(B=B, T=T, process_rank=ddp_rank, num_processes=ddp_world_size, split="train", target_tokens=data_config.target_tokens, tokenizer=tokenizer, dataset_paths=data_config.dataset_paths)
    val_loader = DataLoaderLite(B=B, T=T, process_rank=ddp_rank, num_processes=ddp_world_size, split="test", target_tokens=data_config.target_tokens, tokenizer=tokenizer, dataset_paths=data_config.dataset_paths)
    
    torch.set_float32_matmul_precision('high')
    
    # Create DEBUG model from config
    print("DEBUG: Creating MaskedLanguageModelDebug")
    model = MaskedLanguageModelDebug(model_config, tokenizer=tokenizer)
    model.to(device)
    
    # Check model parameters before compilation/DDP
    print("DEBUG: Model parameters before compilation/DDP:")
    for name, param in model.named_parameters():
        if param.requires_grad:
            print(f"  {name}: shape={param.shape}, requires_grad={param.requires_grad}, device={param.device}")
    
    # Disable torch.compile for debugging to avoid interference
    use_compile = False  # Force disable for debugging
    print("DEBUG: torch.compile disabled for debugging")
    if use_compile:
        print("DEBUG: Compiling model")
        model = torch.compile(model)
    if ddp:
        print("DEBUG: Wrapping model with DDP")
        model = DDP(model, device_ids=[ddp_local_rank], find_unused_parameters=True)
    raw_model = model.module if ddp else model # always contains the "raw" unwrapped model
    
    # Learning rate schedule from config
    max_lr = training_config.max_lr
    min_lr = training_config.min_lr
    warmup_steps = training_config.warmup_steps
    max_steps = training_config.max_steps
    
    def get_lr(it):
        # 1) linear warmup for warmup_iters steps
        if it < warmup_steps:
            return max_lr * (it+1) / warmup_steps
        # 2) if it > lr_decay_iters, return min learning rate
        if it > max_steps:
            return min_lr
        # 3) in between, use cosine decay down to min learning rate
        decay_ratio = (it - warmup_steps) / (max_steps - warmup_steps)
        assert 0 <= decay_ratio <= 1
        coeff = 0.5 * (1.0 + math.cos(math.pi * decay_ratio)) # coeff starts at 1 and goes to 0
        return min_lr + coeff * (max_lr - min_lr)
    
    # Initialize optimizer from config
    print("DEBUG: Configuring optimizer")
    optimizer = raw_model.configure_optimizers(
        weight_decay=training_config.weight_decay, 
        learning_rate=training_config.optimizer_lr, 
        device_type=device_type, 
        embedding_lr_scale=training_config.embedding_lr_scale
    )
    
    # Create log directory using config
    log_dir = logging_config.log_dir
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, logging_config.log_file)
    with open(log_file, "w") as f: # open for writing to clear the file
        pass
    
    # Count trainable parameters
    num_params = sum(p.numel() for p in raw_model.parameters() if p.requires_grad)
    
    if master_process:
        print(f'### starting DEBUG training loop of Encoder-MLM on {data_config.dataset_name} @{datetime.datetime.now()}')
        print(f'Total trainable parameters: {num_params:_}')
        print(f'torch.compile: {use_compile}')
        print(f"=> Batch_size:{B}; Sequence_length:{T}; max_lr:{max_lr}; min_lr:{min_lr}")
        print(f"=> Target tokens: {data_config.target_tokens//1_000_000_000}B; Total steps: {max_steps}; Warmup steps: {warmup_steps}")
        
        writer.add_text('Start of training...', f'### starting DEBUG Encoder-MLM training on {data_config.dataset_name} @{datetime.datetime.now()}; params:{num_params:_}; torch.compile:{use_compile}; \n')
        writer.add_text('HyperParmeters', f"=> Batch_size:{B}; Sequence_length:{T}; max_lr:{max_lr}; min_lr:{min_lr}; Target_tokens:{data_config.target_tokens//1_000_000_000}B; Steps:{max_steps}")
        
        with open(log_file, "a") as f:
            f.write(f'### starting DEBUG Encoder-MLM training on {data_config.dataset_name} @{datetime.datetime.now()}; params:{num_params:_}\n')
            f.write(f"=> Batch_size:{B}; Sequence_length:{T}; max_lr:{max_lr}; min_lr:{min_lr}; Target_tokens:{data_config.target_tokens//1_000_000_000}B; Steps:{max_steps}\n")

    # Training loop - LIMIT TO FIRST FEW STEPS FOR DEBUGGING
    best_val_loss = float('inf')
    debug_max_steps = min(5, max_steps)  # Only run first 5 steps for debugging
    print(f"DEBUG: Running only {debug_max_steps} steps for debugging")
    
    for step in range(debug_max_steps):
        print(f"\n=== DEBUG: Starting step {step} ===")
        t0 = time.time()
        last_step = (step == debug_max_steps - 1)
    
        # Validation evaluation
        if step % 500 == 0 or last_step:
            print(f"DEBUG: Running validation at step {step}")
            model.eval()
            with torch.no_grad():
                val_loss_accum = 0.0
                val_loss_steps = 2  # Reduced for debugging
                for val_step in range(val_loss_steps):
                    print(f"DEBUG: Validation step {val_step}")
                    x, labels, attention_mask = val_loader.next_batch()
                    x, labels, attention_mask = x.to(device), labels.to(device), attention_mask.to(device)
                    
                    print(f"DEBUG: Validation batch shapes - x: {x.shape}, labels: {labels.shape}, attention_mask: {attention_mask.shape}")
                    print(f"DEBUG: Validation batch sample - x[0,:10]: {x[0,:10].tolist()}")
                    print(f"DEBUG: Validation batch sample - labels[0,:10]: {labels[0,:10].tolist()}")
                    print(f"DEBUG: Validation batch sample - attention_mask[0,:10]: {attention_mask[0,:10].tolist()}")
                    
                    with torch.autocast(device_type=device_type, dtype=torch.bfloat16):
                        _, loss = model(x, attention_mask=attention_mask, labels=labels)
                    
                    if loss is not None:
                        val_loss_accum += loss.detach()
                        print(f"DEBUG: Validation step {val_step} loss: {loss.item()}")
                    else:
                        print(f"DEBUG: Validation step {val_step} returned None loss!")
                        
                # Average the accumulated validation loss
                if val_loss_steps > 0:
                    val_loss_accum = val_loss_accum / val_loss_steps
                else:
                    val_loss_accum = torch.tensor(float('nan'))
                    
            if ddp:
                dist.all_reduce(val_loss_accum, op=dist.ReduceOp.AVG)

            if master_process:
                writer.add_scalar("loss/val", val_loss_accum.item(), step)
                print(f"Step {step}: validation loss (MLM): {val_loss_accum.item():.4f}")
                with open(log_file, "a") as f:
                    f.write(f"{step} val_mlm {val_loss_accum.item():.4f}\n")

                torch.cuda.empty_cache()
    
        # Training step
        print(f"DEBUG: Starting training step {step}")
        model.train()
        optimizer.zero_grad()
        loss_accum = 0.0
        
        for micro_step in range(grad_accum_steps):
            print(f"DEBUG: Training micro-step {micro_step}/{grad_accum_steps}")
            x, labels, attention_mask = train_loader.next_batch()
            x, labels, attention_mask = x.to(device), labels.to(device), attention_mask.to(device)
            
            print(f"DEBUG: Training batch shapes - x: {x.shape}, labels: {labels.shape}, attention_mask: {attention_mask.shape}")
            print(f"DEBUG: Training batch sample - x[0,:10]: {x[0,:10].tolist()}")
            print(f"DEBUG: Training batch sample - labels[0,:10]: {labels[0,:10].tolist()}")
            print(f"DEBUG: Training batch sample - attention_mask[0,:10]: {attention_mask[0,:10].tolist()}")
            
            # Validate input tensors before forward pass
            if torch.isnan(x).any() or torch.isnan(labels).any() or torch.isnan(attention_mask).any():
                print(f"NaN detected in inputs at step {step}, micro_step {micro_step}")
                continue
                
            # added after video, this field is also used by the forward pass.
            if ddp:
                model.require_backward_grad_sync = (micro_step == grad_accum_steps - 1)
                
            print(f"DEBUG: Running forward pass for micro-step {micro_step}")
            with torch.autocast(device_type=device_type, dtype=torch.bfloat16):
                _, loss = model(x, attention_mask=attention_mask, labels=labels)

            # Check for NaN loss before backward
            if loss is None:
                print(f"DEBUG: Loss is None at step {step}, micro_step {micro_step}")
                continue
            elif torch.isnan(loss) or torch.isinf(loss):
                print(f"Invalid loss detected at step {step}, micro_step {micro_step}: {loss.item()}")
                continue

            print(f"DEBUG: Loss before scaling: {loss.item()}")
            
            # we have to scale the loss to account for gradient accumulation,
            # because the gradients just add on each successive backward().
            # addition of gradients corresponds to a SUM in the objective, but
            # instead of a SUM we want MEAN. Scale the loss here so it comes out right
            loss = loss / grad_accum_steps
            loss_accum += loss.detach()
            
            print(f"DEBUG: Loss after scaling: {loss.item()}")
            print(f"DEBUG: Running backward pass for micro-step {micro_step}")
            
            try:
                loss.backward()
                print(f"DEBUG: Backward pass completed successfully for micro-step {micro_step}")
            except RuntimeError as e:
                print(f"Backward pass failed at step {step}, micro_step {micro_step}: {e}")
                # Print gradient information for debugging
                print("DEBUG: Checking gradients after failed backward:")
                for name, param in model.named_parameters():
                    if param.requires_grad:
                        grad_status = "has_grad" if param.grad is not None else "no_grad"
                        print(f"  {name}: requires_grad={param.requires_grad}, {grad_status}")
                # Skip this micro-step and continue
                continue
            
        if ddp:
            dist.all_reduce(loss_accum, op=dist.ReduceOp.AVG)
            
        print(f"DEBUG: Clipping gradients")
        norm = torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        print(f"DEBUG: Gradient norm: {norm}")
        
        # determine and set the learning rate for this iteration
        lr = get_lr(step)
        for param_group in optimizer.param_groups:
            param_group['lr'] = lr
            
        print(f"DEBUG: Running optimizer step")
        optimizer.step()
        
        if device_type == "cuda":
            torch.cuda.synchronize() # wait for the GPU to finish work
        t1 = time.time()
        dt = t1 - t0 # time difference in seconds
        tokens_processed = train_loader.B * train_loader.T * grad_accum_steps * ddp_world_size
        tokens_per_sec = tokens_processed / dt
        if master_process:
            writer.add_scalar("loss/train", loss_accum.item(), step)
            writer.add_scalar("norm", norm, step)
            writer.add_scalar("learning_rate", lr, step)
            print(f"step {step:5d} | loss: {loss_accum.item():.6f} | lr {lr:.4e} | norm: {norm:.4f} | dt: {dt*1000:.2f}ms | tok/sec: {tokens_per_sec:.2f}...")
            with open(log_file, "a") as f:
                f.write(f"{step} train {loss_accum.item():.6f} | lr {lr:.4e} | norm: {norm:.4f} | dt: {dt*1000:.2f}ms | tok/sec: {tokens_per_sec:.2f}...\n")

        print(f"=== DEBUG: Completed step {step} ===\n")

    if master_process:
        with open(log_file, "a") as f:
            f.write(f'### end of DEBUG training loop @{datetime.datetime.now()}...\n')
    
    writer.close()
    print(f'### end of DEBUG training loop @{datetime.datetime.now()}\n')
    if ddp:
        destroy_process_group()


if __name__ == "__main__":
    main()
