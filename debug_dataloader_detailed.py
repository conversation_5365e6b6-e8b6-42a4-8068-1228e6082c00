#!/usr/bin/env python3
"""
Detailed debug script to trace dataloader failures step by step
"""

import torch
import sys
import os
from pathlib import Path

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.dataloader import DataLoaderLite

def debug_data_file_discovery():
    """Debug the data file discovery process"""
    print("=== DATA FILE DISCOVERY DEBUG ===")
    
    # Check the default dataset paths
    dataset_paths = ["/s2_nfs/pile/OpenWebText2"]
    
    for dataset_path in dataset_paths:
        print(f"\nChecking dataset path: {dataset_path}")
        base_path = Path(dataset_path)
        print(f"  Base path exists: {base_path.exists()}")
        
        if base_path.exists():
            print(f"  Contents: {list(base_path.iterdir())[:10]}")  # First 10 items
        
        # Check train split
        train_path = base_path / "train"
        print(f"  Train path exists: {train_path.exists()}")
        
        if train_path.exists():
            print(f"  Train contents: {list(train_path.iterdir())[:5]}")  # First 5 items
            
            # Look for arrow files
            arrow_files = list(train_path.glob("*.arrow"))
            print(f"  Arrow files found: {len(arrow_files)}")
            if arrow_files:
                print(f"  Sample arrow files: {arrow_files[:3]}")
        
        # Check test split
        test_path = base_path / "test"
        print(f"  Test path exists: {test_path.exists()}")

def debug_dataloader_initialization():
    """Debug the dataloader initialization process"""
    print("\n=== DATALOADER INITIALIZATION DEBUG ===")
    
    try:
        print("Creating DataLoaderLite...")
        data_loader = DataLoaderLite(
            B=2, 
            T=32, 
            process_rank=0, 
            num_processes=1, 
            split="train"
        )
        print("✓ DataLoader created successfully")
        
        # Check what files were discovered
        print(f"Data files discovered: {len(data_loader.data_files)}")
        for i, file_path in enumerate(data_loader.data_files[:5]):  # First 5 files
            print(f"  File {i}: {file_path}")
            print(f"    Exists: {file_path.exists()}")
            if file_path.exists():
                print(f"    Size: {file_path.stat().st_size} bytes")
        
        return data_loader
        
    except Exception as e:
        print(f"❌ Error creating dataloader: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_file_loading(data_loader):
    """Debug the file loading process"""
    print("\n=== FILE LOADING DEBUG ===")
    
    if not data_loader:
        print("No dataloader to test")
        return
    
    print(f"Current file index: {data_loader.current_file_idx}")
    print(f"Current shard data: {data_loader.current_shard_data}")
    
    # Try to manually load the current file
    try:
        print("Attempting to load current file...")
        data_loader.load_current_file()
        print("✓ File loading completed")
        
        # Check what was loaded
        if hasattr(data_loader, 'raw_texts'):
            print(f"Raw texts loaded: {len(data_loader.raw_texts)}")
            if data_loader.raw_texts:
                print(f"Sample text: '{data_loader.raw_texts[0][:100]}...'")
        else:
            print("No raw_texts attribute after loading")
            
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        import traceback
        traceback.print_exc()

def debug_token_generation(data_loader):
    """Debug token generation process"""
    print("\n=== TOKEN GENERATION DEBUG ===")
    
    if not data_loader:
        print("No dataloader to test")
        return
    
    # Check tokenizer
    print(f"Tokenizer type: {type(data_loader.tokenizer)}")
    print(f"Tokenizer vocab size: {data_loader.tokenizer.vocab_size}")
    
    # Test tokenizer directly
    test_text = "Hello world"
    try:
        tokens = data_loader.tokenizer.encode(test_text, add_special_tokens=False)
        print(f"Test tokenization: '{test_text}' -> {tokens}")
        decoded = data_loader.tokenizer.decode(tokens)
        print(f"Test decoding: {tokens} -> '{decoded}'")
    except Exception as e:
        print(f"❌ Tokenizer test failed: {e}")
    
    # Test get_tokens_for_sequence
    try:
        print("Testing get_tokens_for_sequence...")
        tokens = data_loader.get_tokens_for_sequence()
        if tokens:
            print(f"✓ Generated {len(tokens)} tokens: {tokens[:10]}")
            decoded = data_loader.tokenizer.decode(tokens[:20])
            print(f"Decoded: '{decoded}'")
        else:
            print("❌ get_tokens_for_sequence returned None")
    except Exception as e:
        print(f"❌ Error in get_tokens_for_sequence: {e}")
        import traceback
        traceback.print_exc()

def debug_background_loading(data_loader):
    """Debug background loading thread"""
    print("\n=== BACKGROUND LOADING DEBUG ===")
    
    if not data_loader:
        print("No dataloader to test")
        return
    
    print(f"Loading thread alive: {data_loader.loading_thread.is_alive() if data_loader.loading_thread else 'No thread'}")
    print(f"Token queue size: {data_loader.token_queue.qsize()}")
    print(f"Stop loading flag: {data_loader.stop_loading.is_set()}")
    
    # Wait a bit and check queue again
    import time
    print("Waiting 2 seconds for background loading...")
    time.sleep(2)
    print(f"Token queue size after wait: {data_loader.token_queue.qsize()}")

if __name__ == "__main__":
    print("Starting detailed dataloader debug...")
    
    # Step 1: Check data file discovery
    debug_data_file_discovery()
    
    # Step 2: Test dataloader initialization
    data_loader = debug_dataloader_initialization()
    
    # Step 3: Debug file loading
    debug_file_loading(data_loader)
    
    # Step 4: Debug token generation
    debug_token_generation(data_loader)
    
    # Step 5: Debug background loading
    debug_background_loading(data_loader)
    
    print("\nDetailed debug complete!")
